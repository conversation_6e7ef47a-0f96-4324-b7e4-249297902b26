/**
 * ToolCallHistoryDemo Component
 * Demo component để test hiển thị tool call messages từ lịch sử
 */

import React from 'react';
import ToolCallInterruptMessage from './ToolCallInterruptMessage';
import ToolCallDecisionMessage from './ToolCallDecisionMessage';

const ToolCallHistoryDemo: React.FC = () => {
  // Mock data cho tool call interrupt
  const mockToolCallData = {
    role: 'supervisor',
    toolName: 'web_search',
    toolDescription: 'Search the web for information',
    parameters: {
      query: 'latest AI news',
      limit: 10,
      language: 'vi'
    }
  };

  const mockTimestamp = new Date();

  return (
    <div className="p-6 space-y-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Tool Call History Demo
        </h1>
        
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Tool Call Interrupt Messages
          </h2>
          
          {/* Tool Call Interrupt - Basic */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
              Basic Tool Call Interrupt
            </h3>
            <ToolCallInterruptMessage
              toolCallData={mockToolCallData}
              timestamp={mockTimestamp}
              showDetails={false}
            />
          </div>

          {/* Tool Call Interrupt - With Details */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
              Tool Call Interrupt with Details
            </h3>
            <ToolCallInterruptMessage
              toolCallData={mockToolCallData}
              timestamp={mockTimestamp}
              showDetails={true}
            />
          </div>

          {/* Tool Call Interrupt - No Data */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
              Tool Call Interrupt without Data
            </h3>
            <ToolCallInterruptMessage
              timestamp={mockTimestamp}
              showDetails={false}
            />
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Tool Call Decision Messages
          </h2>
          
          {/* Decision: Yes */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
              Decision: Yes
            </h3>
            <ToolCallDecisionMessage
              decision="yes"
              timestamp={mockTimestamp}
            />
          </div>

          {/* Decision: No */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
              Decision: No
            </h3>
            <ToolCallDecisionMessage
              decision="no"
              timestamp={mockTimestamp}
            />
          </div>

          {/* Decision: Always */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
              Decision: Always
            </h3>
            <ToolCallDecisionMessage
              decision="always"
              timestamp={mockTimestamp}
            />
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Conversation Flow Example
          </h2>
          
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow space-y-4">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Typical Tool Call Flow in Chat History
            </h3>
            
            {/* User message */}
            <div className="flex justify-end">
              <div className="bg-blue-500 text-white p-3 rounded-lg max-w-xs">
                Tìm kiếm tin tức AI mới nhất cho tôi
              </div>
            </div>

            {/* Tool call interrupt */}
            <div className="flex justify-start">
              <div className="max-w-md">
                <ToolCallInterruptMessage
                  toolCallData={mockToolCallData}
                  timestamp={new Date(mockTimestamp.getTime() + 1000)}
                  showDetails={false}
                />
              </div>
            </div>

            {/* User decision */}
            <div className="flex justify-end">
              <div className="max-w-xs">
                <ToolCallDecisionMessage
                  decision="yes"
                  timestamp={new Date(mockTimestamp.getTime() + 2000)}
                />
              </div>
            </div>

            {/* AI response */}
            <div className="flex justify-start">
              <div className="bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white p-3 rounded-lg max-w-md">
                Tôi đã tìm kiếm tin tức AI mới nhất. Đây là những tin tức nổi bật...
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolCallHistoryDemo;
