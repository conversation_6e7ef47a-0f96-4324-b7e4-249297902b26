/**
 * Utility functions để xử lý message content và content blocks
 */

import { HistoryContentBlock, HistoryMessageContent } from '@/shared/types/chat-streaming.types';

/**
 * Kiểm tra xem message có chứa tool call interrupt không
 */
export function hasToolCallInterrupt(content: HistoryMessageContent): boolean {
  return content.contentBlocks.some(block => block.type === 'tool_call_interrupt');
}

/**
 * Kiểm tra xem message có chứa tool call decision không
 */
export function hasToolCallDecision(content: HistoryMessageContent): boolean {
  return content.contentBlocks.some(block => block.type === 'tool_call_decision');
}

/**
 * Lấy tool call interrupt data từ content blocks
 */
export function getToolCallInterruptData(content: HistoryMessageContent): HistoryContentBlock | null {
  return content.contentBlocks.find(block => block.type === 'tool_call_interrupt') || null;
}

/**
 * Lấy tool call decision từ content blocks
 */
export function getToolCallDecision(content: HistoryMessageContent): 'yes' | 'no' | 'always' | null {
  const decisionBlock = content.contentBlocks.find(block => block.type === 'tool_call_decision');
  return decisionBlock?.decision || null;
}

/**
 * Lấy text content từ content blocks (bỏ qua tool call blocks)
 */
export function getTextContentFromBlocks(content: HistoryMessageContent): string {
  const textBlocks = content.contentBlocks.filter(block => block.type === 'text');
  return textBlocks.map(block => block.content || '').join(' ').trim();
}

/**
 * Kiểm tra xem message có phải là tool call related không
 */
export function isToolCallRelatedMessage(content: HistoryMessageContent): boolean {
  return hasToolCallInterrupt(content) || hasToolCallDecision(content);
}

/**
 * Xác định loại message dựa trên content blocks
 */
export function getMessageType(content: HistoryMessageContent): 'text' | 'tool_call_interrupt' | 'tool_call_decision' | 'mixed' {
  const hasInterrupt = hasToolCallInterrupt(content);
  const hasDecision = hasToolCallDecision(content);
  const hasText = content.contentBlocks.some(block => block.type === 'text' && block.content?.trim());

  if (hasInterrupt && !hasDecision && !hasText) {
    return 'tool_call_interrupt';
  }
  
  if (hasDecision && !hasInterrupt && !hasText) {
    return 'tool_call_decision';
  }
  
  if (!hasInterrupt && !hasDecision && hasText) {
    return 'text';
  }
  
  return 'mixed';
}

/**
 * Parse tool call data từ content block
 */
export function parseToolCallData(block: HistoryContentBlock): {
  role?: string;
  toolName?: string;
  toolDescription?: string;
  parameters?: Record<string, unknown>;
  threadId?: string;
  runId?: string;
} | null {
  if (block.type !== 'tool_call_interrupt') {
    return null;
  }

  // Nếu có toolCallData trực tiếp
  if (block.toolCallData) {
    return block.toolCallData;
  }

  // Nếu data được encode trong content (fallback)
  if (block.content) {
    try {
      const parsed = JSON.parse(block.content);
      return {
        role: parsed.role,
        toolName: parsed.toolName,
        toolDescription: parsed.toolDescription,
        parameters: parsed.parameters,
        threadId: parsed.threadId,
        runId: parsed.runId
      };
    } catch (error) {
      console.warn('[parseToolCallData] Failed to parse tool call data from content:', error);
      return null;
    }
  }

  return null;
}

/**
 * Tạo tool call interrupt data từ SSE event format để tương thích với history
 */
export function createToolCallInterruptFromSSE(sseData: {
  role: string;
  toolName: string;
  toolDescription?: string;
  parameters?: Record<string, unknown>;
  threadId: string;
  runId?: string;
}): HistoryContentBlock {
  return {
    type: 'tool_call_interrupt',
    toolCallData: {
      role: sseData.role,
      toolName: sseData.toolName,
      toolDescription: sseData.toolDescription,
      parameters: sseData.parameters,
      threadId: sseData.threadId,
      runId: sseData.runId
    }
  };
}

/**
 * Tạo tool call decision content block
 */
export function createToolCallDecisionBlock(decision: 'yes' | 'no' | 'always'): HistoryContentBlock {
  return {
    type: 'tool_call_decision',
    decision
  };
}
