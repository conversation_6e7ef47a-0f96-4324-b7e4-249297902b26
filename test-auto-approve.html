<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auto Approve Switch</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .switch-demo {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }
        .switch {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            background: #f0f0f0;
            color: #666;
        }
        .switch.active {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }
        .switch-circle {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            transition: all 0.3s;
            background: #999;
        }
        .switch.active .switch-circle {
            background: #3b82f6;
        }
        .switch-text {
            font-size: 12px;
            font-weight: 500;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.enabled {
            background: #d1fae5;
            color: #065f46;
        }
        .status.disabled {
            background: #fee2e2;
            color: #991b1b;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Auto Approve Switch</h1>
        <p>Đây là demo cho switch "Auto" trong ChatInputBox để bật/tắt tự động phê duyệt tool calls.</p>
        
        <div class="switch-demo">
            <span>Auto Approve Switch:</span>
            <div class="switch" id="autoSwitch">
                <div class="switch-circle"></div>
                <span class="switch-text">Auto</span>
            </div>
        </div>
        
        <div class="status" id="status">
            ❌ Auto approve: DISABLED
        </div>
        
        <div class="test-buttons">
            <button class="btn btn-primary" onclick="testLocalStorage()">Check localStorage</button>
            <button class="btn btn-secondary" onclick="clearLocalStorage()">Clear localStorage</button>
        </div>
        
        <div id="localStorage-content">
            <h3>localStorage Content:</h3>
            <pre id="localStorage-data"></pre>
        </div>
    </div>

    <script>
        // Simulate toolApprovalSettingsService
        const STORAGE_KEY = 'tool_approval_settings';
        
        const toolApprovalSettingsService = {
            getSettings() {
                try {
                    const stored = localStorage.getItem(STORAGE_KEY);
                    if (!stored) {
                        return { alwaysApproveAllTools: false };
                    }
                    return JSON.parse(stored);
                } catch (error) {
                    return { alwaysApproveAllTools: false };
                }
            },
            
            enableAlwaysApproveAllTools() {
                const settings = this.getSettings();
                settings.alwaysApproveAllTools = true;
                settings.lastUpdated = Date.now();
                localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
            },
            
            disableAlwaysApproveAllTools() {
                const settings = this.getSettings();
                settings.alwaysApproveAllTools = false;
                settings.lastUpdated = Date.now();
                localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
            },
            
            shouldAlwaysApprove() {
                const settings = this.getSettings();
                return settings.alwaysApproveAllTools || false;
            }
        };
        
        // State
        let isAutoApproveEnabled = false;
        
        // Elements
        const autoSwitch = document.getElementById('autoSwitch');
        const status = document.getElementById('status');
        
        // Initialize
        function init() {
            isAutoApproveEnabled = toolApprovalSettingsService.shouldAlwaysApprove();
            updateUI();
            updateLocalStorageDisplay();
        }
        
        // Update UI
        function updateUI() {
            if (isAutoApproveEnabled) {
                autoSwitch.classList.add('active');
                status.className = 'status enabled';
                status.textContent = '✅ Auto approve: ENABLED';
            } else {
                autoSwitch.classList.remove('active');
                status.className = 'status disabled';
                status.textContent = '❌ Auto approve: DISABLED';
            }
        }
        
        // Toggle auto approve
        function toggleAutoApprove() {
            isAutoApproveEnabled = !isAutoApproveEnabled;
            
            if (isAutoApproveEnabled) {
                toolApprovalSettingsService.enableAlwaysApproveAllTools();
                console.log('✅ Auto approve enabled');
            } else {
                toolApprovalSettingsService.disableAlwaysApproveAllTools();
                console.log('❌ Auto approve disabled');
            }
            
            updateUI();
            updateLocalStorageDisplay();
        }
        
        // Test localStorage
        function testLocalStorage() {
            updateLocalStorageDisplay();
        }
        
        // Clear localStorage
        function clearLocalStorage() {
            localStorage.removeItem(STORAGE_KEY);
            isAutoApproveEnabled = false;
            updateUI();
            updateLocalStorageDisplay();
        }
        
        // Update localStorage display
        function updateLocalStorageDisplay() {
            const data = localStorage.getItem(STORAGE_KEY);
            const display = document.getElementById('localStorage-data');
            if (data) {
                display.textContent = JSON.stringify(JSON.parse(data), null, 2);
            } else {
                display.textContent = 'No data in localStorage';
            }
        }
        
        // Event listeners
        autoSwitch.addEventListener('click', toggleAutoApprove);
        
        // Initialize on load
        init();
    </script>
</body>
</html>
