/**
 * useChatStream Hook
 * Hook chính để quản lý chat streaming với luồng mới
 */

import { ThreadsService } from '@/modules/threads/services';
import { isThreadDeleted } from '@/modules/threads/services/deleted-thread-tracker.service';
import { ChatApiService } from '@/shared/services/chat-api.service';
import { chatConfigService } from '@/shared/services/chat-config.service';
import { ChatSSEService, ToolCallInterruptData } from '@/shared/services/chat-sse.service';
import { toolApprovalSettingsService } from '@/shared/services/tool-approval-settings.service';
import {
  ChatMessage,
  ChatMessageRequest,
  ChatMessageResponse,
  convertThreadMessagesToChatMessages,
  GetMessagesQuery,
  GetMessagesResponseData,
  mapRoleToMessageSender,
  MessageStatus,
} from '@/shared/types/chat-streaming.types';
import { chatApiSingleton } from '@/shared/utils/chatApiSingleton';
import { useCallback, useEffect, useRef, useState } from 'react';
import { flushSync } from 'react-dom';
import { v4 as uuidv4 } from 'uuid';

/**
 * Thread event callbacks
 */
export interface ThreadEventCallbacks {
  onThreadCreated?: (threadId: string, threadName: string) => void;
  onThreadLoaded?: (threadId: string, threadName: string) => void;
  onThreadSwitched?: (fromThreadId: string, toThreadId: string) => void;
  onThreadNameChanged?: (threadId: string, newName: string) => void;
  onThreadDeleted?: (threadId: string) => void;
}

/**
 * Configuration cho useChatStream hook
 */
export interface UseChatStreamConfig {
  agentId?: string;
  apiBaseUrl?: string;
  sseBaseUrl?: string;
  alwaysApproveToolCall?: boolean;
  getAuthToken: () => string | Promise<string>;
  debug?: boolean;

  /**
   * Configuration cho message history
   */
  messageHistory?: {
    pageSize?: number;
    autoLoad?: boolean;
    timeout?: number;
  };

  /**
   * Thread event callbacks
   */
  threadEvents?: ThreadEventCallbacks;

  /**
   * RPoint update callback
   */
  onRPointUpdate?: (rPointCost: number, updatedBalance: string, timestamp: number) => void;
}

/**
 * Return type cho useChatStream hook
 */
export interface UseChatStreamReturn {
  // State
  messages: ChatMessage[];
  isStreaming: boolean;
  isLoading: boolean;
  isThinking: boolean;
  currentStreamingText: string;
  currentRunId: string | null;
  threadId: string | null;
  threadName: string | null;
  isCreatingThread: boolean;
  isLoadingThreads: boolean;

  // Worker thinking state
  workerThinking: {
    isVisible: boolean;
    content: string;
    isStreaming: boolean;
  };

  // Tool call interrupt state
  toolCallInterrupt: ToolCallInterruptData | null;
  approveToolCall: (decision: 'yes' | 'no' | 'always') => Promise<void>;
  dismissToolCallInterrupt: () => void;

  // Message History
  historyMessages: ChatMessage[];
  isLoadingHistory: boolean;
  isLoadingMoreHistory: boolean;
  hasMoreHistory: boolean;
  historyError: string | null;
  totalHistoryItems: number;

  // Actions
  sendMessage: (contentOrRequest: string | ChatMessageRequest, threadId?: string, alwaysApproveToolCall?: boolean) => Promise<ChatMessageResponse | null>;
  updateMessage: (messageId: string, newContent: string) => Promise<void>;
  stopStreaming: () => Promise<void>;
  clearMessages: () => void;
  createNewThread: (name?: string) => Promise<{ threadId: string; threadName: string }>;
  loadLatestThread: () => Promise<void>;
  loadSpecificThread: (threadId: string) => Promise<void>;
  switchToThread: (threadId: string) => Promise<void>;
  getCurrentThreadId: () => string | null;
  updateThreadName: (threadId: string, newName: string) => Promise<void>;
  retryLastMessage: () => Promise<void>;
  removeMessagesAfter: (messageId: string, senderTypes?: string[]) => void;
  removeHistoryMessagesAfter: (messageId: string, senderTypes?: string[]) => void;
  removeSpecificMessages: (messageIds: string[]) => void;
  removeSpecificHistoryMessages: (messageIds: string[]) => void;

  // Message History Actions
  loadMoreHistory: () => Promise<void>;
  refreshHistory: () => Promise<void>;

  // Status
  isConnected: boolean;
  error: string | null;
  streamError: {
    message: string;
    details?: unknown;
    retryContent?: string;
  } | null;
}

/**
 * useChatStream Hook
 */
export function useChatStream(config: UseChatStreamConfig): UseChatStreamReturn {
  // State
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStreamingText, setCurrentStreamingText] = useState('');
  const [currentRunId, setCurrentRunId] = useState<string | null>(null);
  const [threadId, setThreadId] = useState<string | null>(null);
  const [threadName, setThreadName] = useState<string | null>(null);
  const [isCreatingThread, setIsCreatingThread] = useState(false);
  const [isLoadingThreads, setIsLoadingThreads] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isThinking, setIsThinking] = useState(false); // Track thinking state từ tool_call_start/end

  // Worker thinking state
  const [workerThinking, setWorkerThinking] = useState({
    isVisible: false,
    content: '',
    isStreaming: false
  });

  // Current agent ID for message metadata
  const [currentAgentId, setCurrentAgentId] = useState<string | null>(null);

  // Tool call interrupt state
  const [toolCallInterrupt, setToolCallInterrupt] = useState<ToolCallInterruptData | null>(null);

  // Stream error state
  const [streamError, setStreamError] = useState<{
    message: string;
    details?: unknown;
    retryContent?: string;
  } | null>(null);

  // Message History State
  const [historyMessages, setHistoryMessages] = useState<ChatMessage[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [isLoadingMoreHistory, setIsLoadingMoreHistory] = useState(false);
  const [hasMoreHistory, setHasMoreHistory] = useState(true);
  const [historyError, setHistoryError] = useState<string | null>(null);
  const [currentHistoryPage, setCurrentHistoryPage] = useState(1);
  const [totalHistoryItems, setTotalHistoryItems] = useState(0);

  // Services refs
  const apiServiceRef = useRef<ChatApiService | null>(null);
  const sseServiceRef = useRef<ChatSSEService | null>(null);
  const currentStreamingMessageRef = useRef<ChatMessage | null>(null);
  const hasLoadedInitialThreadRef = useRef<boolean>(false);
  const lastMessageContentRef = useRef<string>(''); // Lưu content để retry

  // Debug refs for token tracking
  const tokenCountRef = useRef<number>(0);
  const lastTokenTimeRef = useRef<number>(0);

  // Message History refs
  const currentThreadIdRef = useRef<string | null>(null);
  const isHistoryLoadingRef = useRef(false);
  const historyTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function refs to avoid circular dependencies
  const stopStreamingRef = useRef<(() => Promise<void>) | null>(null);

  // Initialize services
  useEffect(() => {
    const chatConfig = chatConfigService.getConfig();
    const finalConfig = {
      agentId: config.agentId || chatConfig.agentId,
      apiBaseUrl: config.apiBaseUrl || chatConfig.apiBaseUrl,
      sseBaseUrl: config.sseBaseUrl || chatConfig.sseBaseUrl,
      alwaysApproveToolCall: config.alwaysApproveToolCall ?? chatConfig.alwaysApproveToolCall,
      debug: config.debug ?? chatConfig.debug,
      getAuthToken: config.getAuthToken
    };

    // ✅ Update current agent ID
    setCurrentAgentId(finalConfig.agentId);

    // Initialize API service
    apiServiceRef.current = new ChatApiService(
      finalConfig.apiBaseUrl,
      30000, // timeout
      finalConfig.debug
    );

    // Initialize SSE service với debug mode và auth token
    sseServiceRef.current = new ChatSSEService(
      finalConfig.sseBaseUrl,
      true, // Enable debug mode
      finalConfig.getAuthToken // Pass auth token getter
    );

    // Setup SSE callbacks
    sseServiceRef.current.setCallbacks({
      onConnected: (data) => {
        console.log('[useChatStream] SSE Connected:', data);
        setIsConnected(true);
        setError(null);
      },

      onToolCallStart: () => {
        setIsThinking(true);
      },

      onToolCallEnd: () => {
        setIsThinking(false);
      },

      onToolCallInterrupt: (toolCallData) => {
        // Set tool call interrupt state để hiển thị approval form
        setToolCallInterrupt(toolCallData);
      },

      onTextToken: (text: string, role: string) => {

        // ✅ RELAXED CHECK: Accept tokens more liberally để tránh miss tokens
        const hasValidRunId = !!currentRunId;
        const isAfterToolApproval = hasValidRunId && (!currentStreamingMessageRef.current || !isStreaming);
        const hasValidThreadId = !!threadId;
        const shouldAcceptToken = isStreaming || isLoading || isAfterToolApproval || hasValidThreadId;

        if (!shouldAcceptToken) {
          return;
        }

        // ✅ Force enable streaming for any valid token
        if (!isStreaming) {
          setIsLoading(false);
          setIsStreaming(true);
        }

        // Track token frequency
        const now = Date.now();
        tokenCountRef.current += 1;
        lastTokenTimeRef.current = now;

        // Map role từ SSE event thành MessageSender
        const mappedSender = mapRoleToMessageSender(role);

        // ✅ Xử lý worker messages riêng - hiển thị trong thinking box
        if (role === 'worker') {
          setWorkerThinking(prev => {
            const newState = {
              isVisible: true,
              content: prev.content + text,
              isStreaming: true
            };
            return newState;
          });

          // Không tạo message cho worker, chỉ update thinking box
          return;
        }

        // Tạo message mới nếu chưa có hoặc role thay đổi
        if (!currentStreamingMessageRef.current) {

          const newMessage: ChatMessage = {
            id: uuidv4(),
            content: text,
            sender: mappedSender,
            timestamp: new Date(),
            status: MessageStatus.STREAMING,
            threadId: threadId!,
            metadata: {
              ...(currentAgentId && { agentId: currentAgentId }), // ✅ Lưu agentId vào metadata
              originalRole: role, // ✅ Lưu role gốc từ SSE
              streamingRole: role // ✅ Track role hiện tại đang stream
            } as any
          };

          // Force immediate UI update cho message mới
          flushSync(() => {
            setMessages(prev => {
              return [...prev, newMessage];
            });
            setCurrentStreamingText(newMessage.content as string); // ✅ Use full content for consistency
          });

          currentStreamingMessageRef.current = newMessage;
        } else if (currentStreamingMessageRef.current.sender !== mappedSender) {

          // ✅ ONLY split if it's a significant role change (e.g., user -> assistant, assistant -> user)
          // Don't split for minor role variations within AI responses (assistant -> supervisor)
          const shouldSplitMessage = (
            (currentStreamingMessageRef.current.sender === 'user' && mappedSender === 'assistant') ||
            (currentStreamingMessageRef.current.sender === 'assistant' && mappedSender === 'user')
          );

          if (shouldSplitMessage) {
            // Finalize message hiện tại
            const finalizedMessage = {
              ...currentStreamingMessageRef.current,
              status: MessageStatus.COMPLETED
            };

            // Tạo message mới cho role mới
            const newMessage: ChatMessage = {
              id: uuidv4(),
              content: text,
              sender: mappedSender,
              timestamp: new Date(),
              status: MessageStatus.STREAMING,
              threadId: threadId!,
              metadata: {
                ...(currentAgentId && { agentId: currentAgentId }) // ✅ Lưu agentId vào metadata
              }
            };

            // Force immediate UI update
            flushSync(() => {
              setMessages(prev => [
                ...prev.map(msg => msg.id === currentStreamingMessageRef.current?.id ? finalizedMessage : msg),
                newMessage
              ]);
              setCurrentStreamingText(text);
            });

            currentStreamingMessageRef.current = newMessage;
          } else {
            // Continue with same message, just append text
            currentStreamingMessageRef.current.content += text;

            // Update metadata với role mới nhất
            if (currentStreamingMessageRef.current.metadata) {
              (currentStreamingMessageRef.current.metadata as any).streamingRole = role;
            }

            // Force immediate UI update
            setMessages(prev => prev.map(msg =>
              msg.id === currentStreamingMessageRef.current?.id
                ? {
                  ...msg,
                  content: currentStreamingMessageRef.current!.content,
                  metadata: {
                    ...currentStreamingMessageRef.current!.metadata,
                    streamingRole: role as any
                  }
                }
                : msg
            ));

            setCurrentStreamingText(
              typeof currentStreamingMessageRef.current.content === 'string'
                ? currentStreamingMessageRef.current.content
                : ''
            );
          }
        } else {
          currentStreamingMessageRef.current.content += text;

          // Force immediate UI update
          setMessages(prev => prev.map(msg => {
            if (msg.id === currentStreamingMessageRef.current?.id) {
              return {
                ...msg,
                content: currentStreamingMessageRef.current!.content
              };
            }
            return msg;
          }));

          const newStreamingText = typeof currentStreamingMessageRef.current.content === 'string'
            ? currentStreamingMessageRef.current.content
            : '';
          setCurrentStreamingText(newStreamingText);
        }
      },

      onLLMStreamEnd: (role: string) => {
        // ✅ Check if we're still in valid streaming state
        if (!isStreaming && !isLoading) {
          return;
        }

        // ✅ Xử lý worker stream end - chỉ update thinking box
        if (role === 'worker') {
          setWorkerThinking(prev => {
            const newState = {
              ...prev,
              isStreaming: false
            };
            return newState;
          });

          // Thinking box sẽ tự ẩn sau 2000ms
          return;
        }

        if (currentStreamingMessageRef.current) {
          if (role === 'supervisor' || role === 'assistant') {
            // ✅ IMPORTANT: Chỉ update status thành COMPLETED, KHÔNG finalize hoàn toàn
            // Để chờ message_created event gán messageId trước khi finalize
            const updatedMessage: ChatMessage = {
              ...currentStreamingMessageRef.current,
              status: MessageStatus.COMPLETED, // Mark as completed but keep in currentStreamingMessageRef
              metadata: {
                ...currentStreamingMessageRef.current.metadata,
                processingTime: Date.now() - currentStreamingMessageRef.current.timestamp.getTime(),
              }
            };

            setMessages(prev => prev.map(msg =>
              msg.id === currentStreamingMessageRef.current?.id
                ? updatedMessage
                : msg
            ));

            // ✅ Keep reference để message_created có thể assign messageId
            currentStreamingMessageRef.current = updatedMessage;
          }
        }
      },

      onMessageCreated: (messageId: string, role: string, contentPreview: string) => {
        // ✅ Gán messageId cho cả supervisor role và assistant role
        if (currentStreamingMessageRef.current && (role === 'supervisor' || role === 'assistant')) {
          console.log('[useChatStream] 🔗 Assigning messageId to streaming message:', {
            messageId,
            role,
            msgId: currentStreamingMessageRef.current.id,
            isReply: !!currentStreamingMessageRef.current.metadata?.replyToMessageId,
            replyToMessageId: currentStreamingMessageRef.current.metadata?.replyToMessageId
          });

          const finalMessage: ChatMessage = {
            ...currentStreamingMessageRef.current,
            messageId: messageId, // Gán messageId từ API
            status: MessageStatus.COMPLETED, // Ensure message is completed
            timestamp: new Date(), // Update final timestamp
            metadata: {
              ...currentStreamingMessageRef.current.metadata,
              apiMessageId: messageId,
              contentPreview: contentPreview,
            }
          };

          setMessages(prev => prev.map(msg =>
            msg.id === currentStreamingMessageRef.current?.id
              ? finalMessage
              : msg
          ));

          currentStreamingMessageRef.current = finalMessage;
        }

        // ✅ Gán messageId cho user message nếu có trong messages (bao gồm reply messages)
        if (role === 'user') {
          setMessages(prev => prev.map(msg => {
            // Tìm user message cuối cùng chưa có messageId (bao gồm cả reply messages)
            if (msg.sender === 'user' && !msg.messageId) {
              console.log('[useChatStream] 🔗 Assigning messageId to user message:', {
                messageId,
                msgId: msg.id,
                isReply: !!msg.metadata?.replyToMessageId,
                replyToMessageId: msg.metadata?.replyToMessageId
              });

              return {
                ...msg,
                messageId: messageId, // Gán messageId từ API response
                metadata: {
                  ...msg.metadata,
                  apiMessageId: messageId,
                  contentPreview: contentPreview,
                }
              };
            }
            return msg;
          }));
        }
      },

      onStreamSessionEnd: () => {

        // Kết thúc toàn bộ session SSE
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentStreamingText(''); // ✅ Clear cursor
        setCurrentRunId(null);
        setIsConnected(false);
        currentStreamingMessageRef.current = null; // ✅ Clear reference sau khi đã finalize

        // ✅ Reset worker thinking state
        setWorkerThinking({
          isVisible: false,
          content: '',
          isStreaming: false
        });

        // Clear stream error khi session kết thúc thành công
        setStreamError(null);
      },

      onStreamEnd: () => {
        // Fallback cho trường hợp không có stream_session_end
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentStreamingText('');
        setCurrentRunId(null);
        setIsConnected(false);
        currentStreamingMessageRef.current = null;
      },

      onStreamError: (errorMessage: string, errorDetails?: unknown) => {
        // Set stream error state với retry content
        const errorState = {
          message: errorMessage,
          details: errorDetails,
          retryContent: lastMessageContentRef.current
        };
        setStreamError(errorState);

        // Reset streaming state
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setIsConnected(false);
        currentStreamingMessageRef.current = null;

        // ✅ Reset worker thinking state
        setWorkerThinking({
          isVisible: false,
          content: '',
          isStreaming: false
        });
      },

      onUpdateRPoint: (rPointCost: number, updatedBalance: string, timestamp: number) => {

        // Emit RPoint update event để các component khác có thể listen
        if (config.onRPointUpdate) {
          config.onRPointUpdate(rPointCost, updatedBalance, timestamp);
        }
      },

      onError: (error: Error) => {
        // Xử lý timeout error đặc biệt - chỉ hiển thị nếu thực sự timeout
        if (error.message.includes('timeout')) {
          // Kiểm tra xem có đang streaming không - nếu có thì có thể là false positive
          if (isStreaming || isLoading || isThinking) {
            return; // Không set error nếu đang streaming
          }

          setError('Kết nối bị timeout sau 1 phút không có phản hồi. Vui lòng thử lại.');
        } else {
          setError(error.message);
        }

        setIsStreaming(false);
        setIsLoading(false); // Tắt loading khi có lỗi SSE
        setIsThinking(false);
        setIsConnected(false);

        // ✅ Reset worker thinking state
        setWorkerThinking({
          isVisible: false,
          content: '',
          isStreaming: false
        });
      },

      onClose: () => {
        setIsConnected(false);
      }
    });

    return () => {
    };
  }, [config, threadId, currentAgentId]); // ✅ Remove state dependencies to avoid re-render loops

  /**
   * Load messages history từ API
   *
   * IMPORTANT: API trả về messages với sortDirection: 'DESC' (mới nhất trước)
   * Function này sẽ reverse thứ tự để có chronological order (cũ nhất trước)
   */
  const loadMessagesHistory = useCallback(async (
    page: number,
    isLoadMore: boolean = false
  ): Promise<void> => {
    if (!threadId || !apiServiceRef.current) {
      return;
    }

    // Tránh concurrent calls
    if (isLoadingHistory || isLoadingMoreHistory || isHistoryLoadingRef.current) {
      return;
    }

    isHistoryLoadingRef.current = true;

    try {
      if (isLoadMore) {
        setIsLoadingMoreHistory(true);
      } else {
        setIsLoadingHistory(true);
      }
      setHistoryError(null);

      const query: GetMessagesQuery = {
        page,
        limit: config.messageHistory?.pageSize || 20,
        sortBy: 'createdAt',
        sortDirection: 'DESC', // Mới nhất trước
      };

      // Sử dụng singleton để tránh duplicate calls
      const response: GetMessagesResponseData = await chatApiSingleton.executeGetMessages(
        threadId,
        async () => {
          return apiServiceRef.current!.getMessages(threadId, query);
        }
      );

      // Convert ThreadMessageResponseDto thành ChatMessage (API mới)
      const convertedMessages = convertThreadMessagesToChatMessages(response.items);

      // API trả về DESC (mới nhất trước), cần reverse để có chronological order (cũ nhất trước)
      const newChatMessages = convertedMessages.reverse();

      if (isLoadMore) {
        // Load more history = load tin nhắn cũ hơn
        // Append vào đầu danh sách (vì đây là tin nhắn cũ hơn)
        setHistoryMessages(prev => {
          const updated = [...newChatMessages, ...prev];
          return updated;
        });
      } else {
        // Replace toàn bộ danh sách với thứ tự chronological
        setHistoryMessages(newChatMessages);
      }

      // Update pagination state
      setCurrentHistoryPage(response.meta.currentPage);
      setTotalHistoryItems(response.meta.totalItems);
      setHasMoreHistory(response.meta.currentPage < response.meta.totalPages);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load history messages';
      setHistoryError(errorMessage);
    } finally {
      setIsLoadingHistory(false);
      setIsLoadingMoreHistory(false);
      isHistoryLoadingRef.current = false;
    }
  }, [threadId, config.messageHistory?.pageSize, config.debug, isLoadingHistory, isLoadingMoreHistory]);

  /**
   * Load more history messages (next page)
   */
  const loadMoreHistory = useCallback(async (): Promise<void> => {
    if (!hasMoreHistory || isLoadingMoreHistory || isLoadingHistory) {
      return;
    }

    await loadMessagesHistory(currentHistoryPage + 1, true);
  }, [hasMoreHistory, isLoadingMoreHistory, isLoadingHistory, currentHistoryPage, loadMessagesHistory]);

  /**
   * Refresh history messages (reload từ đầu)
   */
  const refreshHistory = useCallback(async (): Promise<void> => {
    if (isLoadingHistory || isLoadingMoreHistory) {
      return;
    }

    setCurrentHistoryPage(1);
    await loadMessagesHistory(1, false);
  }, [isLoadingHistory, isLoadingMoreHistory, loadMessagesHistory]);

  /**
   * Reset history state
   */
  const resetHistory = useCallback(() => {
    setHistoryMessages([]);
    setIsLoadingHistory(false);
    setIsLoadingMoreHistory(false);
    setHasMoreHistory(true);
    setHistoryError(null);
    setCurrentHistoryPage(1);
    setTotalHistoryItems(0);
    currentThreadIdRef.current = null;
  }, []);

  /**
   * Tạo thread mới
   */
  const createNewThread = useCallback(async (name?: string): Promise<{ threadId: string; threadName: string }> => {
    if (!apiServiceRef.current) {
      setError('API service not initialized');
      throw new Error('API service not initialized');
    }

    try {
      setIsCreatingThread(true);
      setError(null);

      // 1. Cancel current SSE and API calls if any
      if (sseServiceRef.current && isConnected) {
        sseServiceRef.current.disconnect();
        setIsConnected(false);
      }

      // 2. Stop current streaming/loading if any
      if (isStreaming || isLoading) {
        if (currentRunId && apiServiceRef.current) {
          try {
            await apiServiceRef.current.stopRun(currentRunId);
          } catch (error) {
            console.warn('[useChatStream] Failed to stop current run:', error);
          }
        }
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentRunId(null);
        currentStreamingMessageRef.current = null;
      }

      // 3. Clear current state
      setMessages([]);
      setCurrentStreamingText('');
      setStreamError(null);
      currentStreamingMessageRef.current = null;
      lastMessageContentRef.current = '';

      // 4. Create new thread
      const response = await apiServiceRef.current.createThread(name);

      // 5. Set new thread state
      setThreadId(response.threadId);
      setThreadName(response.name);

      // 6. Emit thread created event
      config.threadEvents?.onThreadCreated?.(response.threadId, response.name);

      return {
        threadId: response.threadId,
        threadName: response.name
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create thread';
      setError(errorMessage);
      throw error;
    } finally {
      setIsCreatingThread(false);
    }
  }, [config.threadEvents, isConnected, isStreaming, isLoading, currentRunId]);

  /**
   * Load thread mới nhất (theo createdAt DESC)
   */
  const loadLatestThread = useCallback(async () => {
    if (!apiServiceRef.current) {
      setError('API service not initialized');
      return;
    }

    try {
      setIsLoadingThreads(true);
      setError(null);

      // Sử dụng singleton để tránh duplicate calls
      const response = await chatApiSingleton.executeGetThreads(async () => {
        return apiServiceRef.current!.getThreads({
          page: 1,
          limit: 1,
          sortBy: 'createdAt',
          sortDirection: 'DESC'
        });
      });

      if (response.items.length > 0) {
        const latestThread = response.items[0];
        setThreadId(latestThread!.threadId);
        setThreadName(latestThread!.name);
      } else {
        // Không có thread nào, tự động tạo thread mới
        try {
          // Tạo thread trực tiếp qua API service
          const response = await apiServiceRef.current!.createThread('Cuộc trò chuyện mới');
          setThreadId(response.threadId);
          setThreadName(response.name);

          // Emit thread created event
          config.threadEvents?.onThreadCreated?.(response.threadId, response.name);
        } catch (error) {
          // Vẫn set null để có thể tạo sau
          setThreadId(null);
          setThreadName(null);
        }
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load latest thread';
      setError(errorMessage);

      // Không tạo thread mới ở đây, để user tự tạo khi cần
      setThreadId(null);
      setThreadName(null);
    } finally {
      setIsLoadingThreads(false);
    }
  }, [config.threadEvents]); // Add config.threadEvents dependency

  // Auto-load latest thread khi khởi tạo - chỉ chạy một lần
  useEffect(() => {
    // Kiểm tra singleton để tránh multiple initialization
    if (chatApiSingleton.isInitialized()) {
      return;
    }

    // Thêm timeout để tránh React StrictMode double execution
    const timeoutId = setTimeout(() => {
      if (!hasLoadedInitialThreadRef.current && apiServiceRef.current && !isLoadingThreads && !isCreatingThread) {
        chatApiSingleton.setInitialized();
        hasLoadedInitialThreadRef.current = true;
        loadLatestThread();
      }
    }, 100); // Tăng delay lên 100ms

    return () => clearTimeout(timeoutId);
  }, [loadLatestThread, isLoadingThreads, isCreatingThread]);

  // Auto load history khi threadId thay đổi
  useEffect(() => {
    // Clear previous timeout
    if (historyTimeoutRef.current) {
      clearTimeout(historyTimeoutRef.current);
    }

    // Kiểm tra xem threadId có thay đổi thực sự không
    if (currentThreadIdRef.current === threadId) {
      return;
    }

    currentThreadIdRef.current = threadId;

    if (threadId && (config.messageHistory?.autoLoad ?? true)) {

      // Debounce API call
      historyTimeoutRef.current = setTimeout(() => {
        loadMessagesHistory(1, false);
      }, 100); // 100ms debounce
    } else if (!threadId) {
      resetHistory();
    }

    // Cleanup timeout on unmount
    return () => {
      if (historyTimeoutRef.current) {
        clearTimeout(historyTimeoutRef.current);
      }
    };
  }, [threadId, config.messageHistory?.autoLoad, loadMessagesHistory, resetHistory]);

  // ✅ Check for tool call interrupts in history messages and trigger approval form
  useEffect(() => {
    // Import utility functions
    import('@/shared/utils/message-content.utils').then(({ hasToolCallInterrupt, getToolCallInterruptData, parseToolCallData }) => {
      // Check if any history message has tool call interrupt that hasn't been resolved
      const interruptMessage = historyMessages.find(message => {
        if (message.content && typeof message.content === 'object' && 'contentBlocks' in message.content) {
          return hasToolCallInterrupt(message.content as any);
        }
        return false;
      });

      // Check if there's a corresponding decision message after the interrupt
      let hasDecisionAfterInterrupt = false;
      if (interruptMessage) {
        const interruptIndex = historyMessages.findIndex(msg => msg.id === interruptMessage.id);
        // Look for decision message after the interrupt
        for (let i = interruptIndex + 1; i < historyMessages.length; i++) {
          const msg = historyMessages[i];
          if (msg && msg.content && typeof msg.content === 'object' && 'contentBlocks' in msg.content) {
            const hasDecision = (msg.content as any).contentBlocks?.some((block: any) => block.type === 'tool_call_decision');
            if (hasDecision) {
              hasDecisionAfterInterrupt = true;
              break;
            }
          }
        }
      }

      // Only trigger approval form if there's an interrupt without a decision
      if (interruptMessage && !hasDecisionAfterInterrupt && !toolCallInterrupt) {
        // Found unresolved tool call interrupt in history, trigger approval form
        const historyContent = interruptMessage.content as any;
        const interruptData = getToolCallInterruptData(historyContent);

        if (interruptData) {
          const toolCallData = parseToolCallData(interruptData);
          if (toolCallData) {
            // Create ToolCallInterruptData compatible object
            const toolCallInterruptData: ToolCallInterruptData = {
              role: toolCallData.role || 'supervisor',
              toolName: toolCallData.toolName || 'unknown_tool',
              toolDescription: toolCallData.toolDescription || 'Tool call from history',
              parameters: toolCallData.parameters || {},
              threadId: toolCallData.threadId || threadId || ''
            };

            // Add runId if available
            if (toolCallData.runId) {
              (toolCallInterruptData as any).runId = toolCallData.runId;
            }

            console.log('[useChatStream] 🔧 Found unresolved tool call interrupt in history, triggering approval form:', toolCallInterruptData);
            setToolCallInterrupt(toolCallInterruptData);
          }
        }
      }
    });
  }, [historyMessages, toolCallInterrupt, threadId]);

  /**
   * Gửi tin nhắn - hỗ trợ cả legacy string và ChatMessageRequest mới
   */
  const sendMessage = useCallback(async (
    contentOrRequest: string | ChatMessageRequest,
    threadIdParam?: string,
    alwaysApproveToolCall: boolean = false
  ): Promise<ChatMessageResponse | null> => {
    if (!apiServiceRef.current || !sseServiceRef.current) {
      setError('Services not initialized');
      return null;
    }

    // ✅ Check localStorage for always approve setting
    const shouldAlwaysApprove = toolApprovalSettingsService.shouldAlwaysApprove(
      currentAgentId ? { agentId: currentAgentId } : {}
    );

    // Override alwaysApproveToolCall if localStorage setting is enabled
    const finalAlwaysApprove = shouldAlwaysApprove || alwaysApproveToolCall;

    // Hủy streaming/loading hiện tại nếu có
    if (isStreaming || isLoading) {
      try {
        await stopStreaming();
      } catch (error) {
        console.warn('[useChatStream] Failed to stop current operation:', error);
      }
    }

    // Xử lý theo loại request
    let isLegacyRequest = typeof contentOrRequest === 'string';
    let currentThreadId: string;
    let messageContent: string;

    if (isLegacyRequest) {
      // Legacy string request
      messageContent = contentOrRequest as string;
      currentThreadId = threadIdParam || threadId || '';

      // Tạo thread nếu chưa có
      if (!currentThreadId) {
        try {
          const newThread = await createNewThread('Cuộc trò chuyện mới');
          currentThreadId = newThread.threadId;
        } catch (error) {
          setError('Failed to create thread. Please try again.');
          return null;
        }
      }
    } else {
      // ChatMessageRequest object
      const messageRequest = contentOrRequest as ChatMessageRequest;
      currentThreadId = messageRequest.threadId || threadId || '';

      // ✅ Set alwaysApproveToolCall từ localStorage nếu chưa có
      if (messageRequest.alwaysApproveToolCall === undefined) {
        messageRequest.alwaysApproveToolCall = finalAlwaysApprove;
      }

      // Lấy content từ content block đầu tiên
      const firstTextBlock = messageRequest.contentBlocks?.find((block: any) => block.type === 'text');
      messageContent = firstTextBlock?.content || '';

      // Tạo thread nếu chưa có
      if (!currentThreadId) {
        try {
          const newThread = await createNewThread('Cuộc trò chuyện mới');
          currentThreadId = newThread.threadId;
          messageRequest.threadId = currentThreadId;
        } catch (error) {
          setError('Failed to create thread. Please try again.');
          return null;
        }
      }
    }

    try {
      setError(null);
      setStreamError(null);
      setIsLoading(true);

      // Lưu content để có thể retry
      lastMessageContentRef.current = messageContent;

      // Hiển thị tin nhắn user (chỉ khi không phải modify)
      const shouldShowUserMessage = isLegacyRequest || !(contentOrRequest as ChatMessageRequest).messageId;
      if (shouldShowUserMessage) {
        // Extract reply info từ request nếu có
        const replyToMessageId = !isLegacyRequest ? (contentOrRequest as ChatMessageRequest).replyToMessageId : undefined;

        // Tìm reply content từ messages hiện tại nếu có replyToMessageId
        let replyContent: string | undefined;
        if (replyToMessageId) {
          // Tìm trong cả historyMessages và messages hiện tại
          const allCurrentMessages = [...historyMessages, ...messages];
          const replyMessage = allCurrentMessages.find(msg =>
            msg.messageId === replyToMessageId || msg.id === replyToMessageId
          );
          if (replyMessage) {
            replyContent = typeof replyMessage.content === 'string'
              ? replyMessage.content
              : String(replyMessage.content);
          }
        }

        const userMessage: ChatMessage = {
          id: uuidv4(),
          content: messageContent,
          sender: 'user',
          timestamp: new Date(),
          status: MessageStatus.SENT,
          threadId: currentThreadId,
          metadata: {
            ...(replyToMessageId && {
              replyToMessageId,
              ...(replyContent && { replyContent })
            })
          }
        };
        setMessages(prev => [...prev, userMessage]);
      }

      // Chuẩn bị cho streaming
      currentStreamingMessageRef.current = null;
      setCurrentStreamingText('');
      tokenCountRef.current = 0;
      lastTokenTimeRef.current = Date.now();

      const response = await apiServiceRef.current.sendMessage(
        contentOrRequest,
        isLegacyRequest ? currentThreadId : undefined,
        isLegacyRequest ? finalAlwaysApprove : undefined
      );

      // Lưu runId từ response
      const newRunId = response.runId;
      setCurrentRunId(newRunId);

      // Connect SSE để nhận stream
      console.log('[useChatStream] 🔌 Connecting SSE...', { threadId: currentThreadId, runId: newRunId });
      await sseServiceRef.current.connect(currentThreadId, newRunId);

      // Set current message ID cho queue-based streaming (sử dụng runId làm messageId tạm thời)
      sseServiceRef.current.setCurrentMessageId(newRunId);

      return response;

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
      setError(errorMessage);
      setIsStreaming(false);
      setIsLoading(false);
      setIsThinking(false);

      // Remove the failed AI message
      if (currentStreamingMessageRef.current) {
        setMessages(prev => prev.filter(msg => msg.id !== currentStreamingMessageRef.current?.id));
        currentStreamingMessageRef.current = null;
      }

      return null;
    }
  }, [threadId, createNewThread, isLoading, isStreaming]);

  /**
   * Dừng streaming hiện tại
   */
  const stopStreaming = useCallback(async () => {
    try {
      console.log('[useChatStream] 🛑 STOPPING STREAMING/LOADING...', {
        currentRunId,
        isStreaming,
        isLoading,
        isConnected
      });

      // 1. ✅ Reset state IMMEDIATELY để prevent further event processing
      setIsStreaming(false);
      setIsLoading(false);
      setIsThinking(false);
      setCurrentRunId(null);
      currentStreamingMessageRef.current = null;

      // 1.5. ✅ Clear any pending timeouts
      if (historyTimeoutRef.current) {
        clearTimeout(historyTimeoutRef.current);
        historyTimeoutRef.current = null;
      }

      // 2. ✅ Disconnect SSE (luôn thực hiện)
      if (sseServiceRef.current && isConnected) {
        sseServiceRef.current.disconnect();
        setIsConnected(false);
      }

      // 3. ✅ Call API để dừng run (chỉ khi có currentRunId và apiService)
      if (currentRunId && apiServiceRef.current) {
        await apiServiceRef.current.stopRun(currentRunId);
      }

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to stop streaming';
      setError(errorMessage);

      // ✅ Vẫn reset state ngay cả khi có lỗi
      setIsStreaming(false);
      setIsLoading(false);
      setIsThinking(false);
      setCurrentRunId(null);
      setIsConnected(false);
      currentStreamingMessageRef.current = null;
    }
  }, [currentRunId, isStreaming, isLoading, isConnected]);

  /**
   * Send retry message without creating new user message
   */
  const sendRetryMessage = useCallback(async (content: string) => {
    if (!threadId) {
      return;
    }

    try {
      setError(null);
      setStreamError(null); // Clear previous stream error
      setIsLoading(true); // Bắt đầu loading

      // 1. Chuẩn bị cho streaming (message sẽ được tạo khi nhận token đầu tiên)
      currentStreamingMessageRef.current = null;
      setCurrentStreamingText('');

      // Reset streaming state
      tokenCountRef.current = 0;
      lastTokenTimeRef.current = Date.now();

      // 2. Call API gửi tin nhắn (KHÔNG tạo user message trong UI)
      const response = await apiServiceRef.current!.sendMessage(content, threadId);

      // 3. Lưu runId và bắt đầu streaming
      setCurrentRunId(response.runId);
      setIsLoading(false); // Tắt loading, bắt đầu streaming
      setIsStreaming(true);

      // 4. Connect SSE để nhận streaming response
      await sseServiceRef.current!.connect(threadId, response.runId);

      // Set current message ID cho queue-based streaming
      sseServiceRef.current!.setCurrentMessageId(response.runId);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to retry message');
      setIsLoading(false);
      setIsStreaming(false);
      setIsThinking(false);
    }
  }, [threadId]);

  /**
   * Retry last message khi có stream error
   */
  const retryLastMessage = useCallback(async () => {
    if (!streamError?.retryContent) {
      return;
    }

    // Clear stream error và retry
    setStreamError(null);

    await sendRetryMessage(streamError.retryContent);
  }, [streamError, sendRetryMessage, messages.length]);

  /**
   * Load specific thread by ID
   */
  const loadSpecificThread = useCallback(async (targetThreadId: string) => {
    try {
      setIsLoadingThreads(true);
      setError(null);

      // Check if thread is deleted before making API call
      if (isThreadDeleted(targetThreadId)) {
        throw new Error(`Thread ${targetThreadId} has been deleted`);
      }

      // Verify thread exists by getting its detail using ThreadsService
      try {
        const threadDetail = await ThreadsService.getThreadDetail(targetThreadId);

        if (threadDetail) {
          // Clear current messages
          setMessages([]);
          setCurrentStreamingText('');
          currentStreamingMessageRef.current = null;
          setStreamError(null);

          // Set new thread
          setThreadId(targetThreadId);
          setThreadName(threadDetail.name);

          // Emit thread loaded event
          config.threadEvents?.onThreadLoaded?.(targetThreadId, threadDetail.name);

        } else {
          throw new Error('Thread not found');
        }
      } catch (apiError: unknown) {
        // Nếu thread không tồn tại (404), có thể đã bị xóa
        if (apiError instanceof Error && apiError.message.includes('404')) {
          throw new Error(`Thread ${targetThreadId} not found (possibly deleted)`);
        }
        // Re-throw other errors
        throw apiError;
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load thread';
      setError(errorMessage);
    } finally {
      setIsLoadingThreads(false);
    }
  }, [config.threadEvents]);

  /**
   * Get current thread ID
   */
  const getCurrentThreadId = useCallback((): string | null => {
    return threadId;
  }, [threadId]);

  /**
   * Update thread name
   */
  const updateThreadName = useCallback(async (targetThreadId: string, newName: string) => {
    if (!apiServiceRef.current) {
      setError('API service not initialized');
      throw new Error('API service not initialized');
    }

    try {
      // Update via ThreadsService
      const response = await ThreadsService.updateThread(targetThreadId, { name: newName });

      // Update local state if this is current thread
      if (threadId === targetThreadId) {
        console.log('[useChatStream] Updating local thread name:', {
          oldName: threadName,
          newName: response.name,
          threadId,
          targetThreadId
        });
        setThreadName(response.name);
      }

      // Emit thread name changed event
      config.threadEvents?.onThreadNameChanged?.(targetThreadId, response.name);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update thread name';
      setError(errorMessage);
      throw error;
    }
  }, [threadId, threadName, config.threadEvents]);

  /**
   * Switch to a different thread (disconnect current SSE and load new thread)
   */
  const switchToThread = useCallback(async (newThreadId: string) => {
    try {

      // Nếu đã là thread hiện tại, không cần làm gì
      if (threadId === newThreadId) {
        return;
      }

      const previousThreadId = threadId;

      // 1. Disconnect SSE hiện tại nếu có
      if (sseServiceRef.current && isConnected) {
        sseServiceRef.current.disconnect();
        setIsConnected(false);
      }

      // 2. Stop streaming hiện tại nếu có
      if (isStreaming || isLoading) {
        if (currentRunId && apiServiceRef.current) {
          try {
            await apiServiceRef.current.stopRun(currentRunId);
          } catch (error) {
            console.warn('[useChatStream] Failed to stop current run:', error);
          }
        }
        setIsStreaming(false);
        setIsLoading(false);
        setIsThinking(false);
        setCurrentRunId(null);
        currentStreamingMessageRef.current = null;
      }

      // 3. Clear current messages và state
      setMessages([]);
      setCurrentStreamingText('');
      setStreamError(null);
      setError(null);
      currentStreamingMessageRef.current = null;
      lastMessageContentRef.current = '';

      // 4. Load new thread
      try {
        await loadSpecificThread(newThreadId);

        // 5. Emit thread switched event
        if (previousThreadId) {
          config.threadEvents?.onThreadSwitched?.(previousThreadId, newThreadId);
        }
      } catch (loadError: unknown) {
        // Nếu không load được thread (có thể đã bị xóa), clear state
        setThreadId(null);
        setThreadName(null);

        // Emit error event
        const errorMessage = loadError instanceof Error ? loadError.message : 'Failed to load thread';
        setError(errorMessage);

        // Vẫn emit switched event để parent component biết thread switch failed
        if (previousThreadId) {
          config.threadEvents?.onThreadSwitched?.(previousThreadId, '');
        }

        throw loadError;
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to switch thread';
      setError(errorMessage);
    }
  }, [threadId, isConnected, isStreaming, isLoading, currentRunId, loadSpecificThread, config.threadEvents]);

  /**
   * Clear tất cả messages và reset thread
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentStreamingText('');
    setThreadId(null);
    setThreadName(null);
    currentStreamingMessageRef.current = null;
    lastMessageContentRef.current = '';
    setStreamError(null);

    // Reset flag để có thể load thread mới
    hasLoadedInitialThreadRef.current = false;
    // Reset message history
    resetHistory();
  }, [resetHistory]);

  // Assign stopStreaming to ref để có thể gọi từ sendMessage
  useEffect(() => {
    stopStreamingRef.current = stopStreaming;
  }, [stopStreaming]);

  /**
   * Approve tool call với decision
   */
  const approveToolCall = useCallback(async (decision: 'yes' | 'no' | 'always') => {
    if (!toolCallInterrupt || !apiServiceRef.current || !sseServiceRef.current) {
      console.warn('[useChatStream] No tool call interrupt to approve');
      return;
    }

    try {
      // Call approve API
      const response = await apiServiceRef.current.approveToolCall(toolCallInterrupt.threadId, decision);

      // Clear tool call interrupt state
      setToolCallInterrupt(null);

      // Reconnect SSE với runId mới nếu có
      if (response.runId && response.runId !== currentRunId) {

        // ✅ Update runId BEFORE disconnecting to avoid race condition
        // const oldRunId = currentRunId;
        setCurrentRunId(response.runId);

        // ✅ Prepare for new stream without losing current message
        setIsLoading(true);
        // Keep isStreaming as is to maintain token acceptance

        // ✅ IMPORTANT: Không clear currentStreamingMessageRef vì có thể đang có message đang stream
        // currentStreamingMessageRef.current = null; // ← DON'T DO THIS
        try {
          // Disconnect old connection after new one is ready
          sseServiceRef.current.disconnect();

          await sseServiceRef.current.connect(toolCallInterrupt.threadId, response.runId);
        } catch (connectError) {
          setError('Failed to reconnect to streaming service');
        }
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to approve tool call');
    }
  }, [toolCallInterrupt, currentRunId]);

  /**
   * Dismiss tool call interrupt without approval
   */
  const dismissToolCallInterrupt = useCallback(() => {
    setToolCallInterrupt(null);
  }, []);

  /**
   * Update message content and send to get AI response (for edit functionality)
   */
  const updateMessage = useCallback(async (messageId: string, newContent: string) => {
    console.log('[useChatStream] 📝 Updating message and sending:', { messageId, newContent });

    if (!threadId || !apiServiceRef.current || !sseServiceRef.current) {
      throw new Error('Required services not available');
    }

    try {
      // Debug: Log all available messages
      console.log('[useChatStream] 🔍 DEBUG - Available messages before update:', {
        messageIdToFind: messageId,
        currentMessages: messages.map(m => ({
          id: m.id,
          messageId: m.messageId,
          sender: m.sender,
          content: typeof m.content === 'string' ? m.content.substring(0, 50) + '...' : '[ContentBlocks]'
        })),
        historyMessages: historyMessages.map(m => ({
          id: m.id,
          messageId: m.messageId,
          sender: m.sender,
          content: typeof m.content === 'string' ? m.content.substring(0, 50) + '...' : '[ContentBlocks]'
        })),
        totalCurrent: messages.length,
        totalHistory: historyMessages.length
      });

      // 1. Update message content locally first (cả current và history messages)
      let messageFound = false;
      let foundMessageApiId: string | undefined;

      // Update trong current messages
      setMessages(prev => prev.map(msg => {
        if (msg.id === messageId) {
          messageFound = true;
          foundMessageApiId = msg.messageId;
          console.log('[useChatStream] Found message in current messages:', {
            id: msg.id,
            messageId: msg.messageId,
            hasApiId: !!msg.messageId
          });

          return {
            ...msg,
            content: newContent,
            timestamp: new Date(), // Update timestamp
            metadata: {
              ...msg.metadata,
              edited: true,
              editedAt: new Date().toISOString()
            }
          };
        }
        return msg;
      }));

      // Update trong history messages nếu không tìm thấy trong current
      if (!messageFound) {
        setHistoryMessages(prev => prev.map(msg => {
          if (msg.id === messageId) {
            messageFound = true;
            foundMessageApiId = msg.messageId;
            console.log('[useChatStream] Found message in history messages:', {
              id: msg.id,
              messageId: msg.messageId,
              hasApiId: !!msg.messageId
            });

            return {
              ...msg,
              content: newContent,
              timestamp: new Date(), // Update timestamp
              metadata: {
                ...msg.metadata,
                edited: true,
                editedAt: new Date().toISOString()
              }
            };
          }
          return msg;
        }));
      }

      // 2. Prepare for streaming response
      setError(null);
      setStreamError(null);
      setIsLoading(true);

      // Save content for retry
      lastMessageContentRef.current = newContent;

      // Reset streaming state
      currentStreamingMessageRef.current = null;
      setCurrentStreamingText('');
      tokenCountRef.current = 0;
      lastTokenTimeRef.current = Date.now();

      // 3. Sử dụng foundMessageApiId hoặc fallback
      const apiMessageId = foundMessageApiId || messageId;
      console.log('[useChatStream] 📤 Modifying existing message:', {
        messageId,
        apiMessageId,
        foundMessageApiId,
        newContent
      });

      const response = await apiServiceRef.current.modifyMessage(newContent, threadId, messageId);

      // 5. Start streaming for AI response
      setCurrentRunId(response.runId);
      setIsLoading(false);
      setIsStreaming(true);

      await sseServiceRef.current.connect(threadId, response.runId);
      sseServiceRef.current.setCurrentMessageId(response.runId);

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update message';
      setError(errorMessage);
      setIsStreaming(false);
      setIsLoading(false);
      setIsThinking(false);
      throw error;
    }
  }, [threadId]);

  /**
   * Remove messages after a specific message ID (for edit functionality)
   */
  const removeMessagesAfter = useCallback((messageId: string, senderTypes: string[] = ['assistant', 'supervisor', 'worker']) => {
    setMessages(prev => {
      const messageIndex = prev.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        console.warn('[useChatStream] Message not found for removeMessagesAfter:', messageId);
        return prev;
      }

      // Lấy tất cả messages sau message được chỉ định
      const messagesToKeep = prev.slice(0, messageIndex + 1);
      const messagesToRemove = prev.slice(messageIndex + 1)
        .filter(msg => senderTypes.includes(msg.sender));

      console.log('[useChatStream] Removing messages after edit:', {
        messageId,
        messagesToRemove: messagesToRemove.map(m => ({ id: m.id, sender: m.sender })),
        totalRemoved: messagesToRemove.length
      });

      // Trả về messages đã lọc (giữ lại messages trước đó + messages không phải assistant)
      const remainingMessages = prev.slice(messageIndex + 1)
        .filter(msg => !senderTypes.includes(msg.sender));

      return [...messagesToKeep, ...remainingMessages];
    });
  }, []);

  /**
   * Remove history messages after a specific message ID (for edit functionality)
   */
  const removeHistoryMessagesAfter = useCallback((messageId: string, senderTypes: string[] = ['assistant', 'supervisor', 'worker']) => {
    setHistoryMessages(prev => {
      const messageIndex = prev.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        console.warn('[useChatStream] History message not found for removeHistoryMessagesAfter:', messageId);
        return prev;
      }

      // Lấy tất cả history messages sau message được chỉ định
      const messagesToKeep = prev.slice(0, messageIndex + 1);
      const messagesToRemove = prev.slice(messageIndex + 1)
        .filter(msg => senderTypes.includes(msg.sender));

      console.log('[useChatStream] Removing history messages after edit:', {
        messageId,
        messagesToRemove: messagesToRemove.map(m => ({ id: m.id, sender: m.sender })),
        totalRemoved: messagesToRemove.length
      });

      // Trả về history messages đã lọc (giữ lại messages trước đó + messages không phải assistant)
      const remainingMessages = prev.slice(messageIndex + 1)
        .filter(msg => !senderTypes.includes(msg.sender));

      return [...messagesToKeep, ...remainingMessages];
    });
  }, []);

  /**
   * Remove specific messages by IDs (for edit functionality)
   */
  const removeSpecificMessages = useCallback((messageIds: string[]) => {
    setMessages(prev => {
      const filtered = prev.filter(msg => !messageIds.includes(msg.id));
      console.log('[useChatStream] Removing specific messages:', {
        messageIds,
        removedCount: prev.length - filtered.length,
        remainingCount: filtered.length
      });
      return filtered;
    });
  }, []);

  /**
   * Remove specific history messages by IDs (for edit functionality)
   */
  const removeSpecificHistoryMessages = useCallback((messageIds: string[]) => {
    setHistoryMessages(prev => {
      const filtered = prev.filter(msg => !messageIds.includes(msg.id));
      console.log('[useChatStream] Removing specific history messages:', {
        messageIds,
        removedCount: prev.length - filtered.length,
        remainingCount: filtered.length
      });
      return filtered;
    });
  }, []);

  return {
    // State
    messages,
    isStreaming,
    isLoading,
    isThinking,
    currentStreamingText,
    currentRunId,
    threadId,
    threadName,
    isCreatingThread,
    isLoadingThreads,

    // Worker thinking state
    workerThinking,

    // Tool call interrupt state
    toolCallInterrupt,
    approveToolCall,
    dismissToolCallInterrupt,

    // Message History
    historyMessages,
    isLoadingHistory,
    isLoadingMoreHistory,
    hasMoreHistory,
    historyError,
    totalHistoryItems,

    // Actions
    sendMessage,
    updateMessage,
    stopStreaming,
    clearMessages,
    createNewThread,
    loadLatestThread,
    loadSpecificThread,
    switchToThread,
    getCurrentThreadId,
    updateThreadName,
    retryLastMessage,
    removeMessagesAfter,
    removeHistoryMessagesAfter,
    removeSpecificMessages,
    removeSpecificHistoryMessages,

    // Message History Actions
    loadMoreHistory,
    refreshHistory,

    // Status
    isConnected,
    error,
    streamError
  };
}

