/**
 * Demo component để test API gửi tin nhắn mới
 * Hỗ trợ reply, modify, attachments
 */

import React, { useState } from 'react';
import { Button, Input, Textarea, Card, Typography } from '@/shared/components/common';
import { useChatStream } from '@/shared/hooks/common/useChatStream';

interface ChatMessageDemoProps {
  threadId?: string;
}

const ChatMessageDemo: React.FC<ChatMessageDemoProps> = ({ threadId: initialThreadId }) => {
  const [content, setContent] = useState('');
  const [replyToMessageId, setReplyToMessageId] = useState('');
  const [messageId, setMessageId] = useState('');
  const [attachmentType, setAttachmentType] = useState<'file' | 'image' | ''>('');
  const [attachmentId, setAttachmentId] = useState('');

  const chatStream = useChatStream({
    getAuthToken: () => localStorage.getItem('authToken') || '',
    debug: true
  });

  const handleSendMessage = async () => {
    if (!content.trim()) return;

    const messageRequest: any = {
      contentBlocks: [
        {
          type: 'text',
          content: content.trim()
        }
      ],
      threadId: initialThreadId || chatStream.threadId || 'demo-thread-id'
    };

    // Thêm reply nếu có
    if (replyToMessageId.trim()) {
      messageRequest.replyToMessageId = replyToMessageId.trim();
    }

    // Thêm messageId để modify nếu có
    if (messageId.trim()) {
      messageRequest.messageId = messageId.trim();
    }

    // Thêm attachment nếu có
    if (attachmentType && attachmentId.trim()) {
      if (attachmentType === 'file') {
        messageRequest.contentBlocks.push({
          type: 'file',
          fileId: attachmentId.trim()
        });
      } else if (attachmentType === 'image') {
        messageRequest.contentBlocks.push({
          type: 'image',
          imageId: attachmentId.trim()
        });
      }

      messageRequest.attachmentContext = [
        {
          type: attachmentType,
          [attachmentType === 'file' ? 'fileId' : 'imageId']: attachmentId.trim()
        }
      ];
    }

    try {
      const response = await chatStream.sendMessageWithDto(messageRequest);
      console.log('Message sent successfully:', response);
      
      // Clear form
      setContent('');
      setReplyToMessageId('');
      setMessageId('');
      setAttachmentId('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleSendSimpleMessage = async () => {
    if (!content.trim()) return;

    try {
      await chatStream.sendMessage(content.trim());
      setContent('');
    } catch (error) {
      console.error('Failed to send simple message:', error);
    }
  };

  return (
    <Card className="p-6 max-w-2xl mx-auto">
      <Typography variant="h5" className="mb-4">
        Chat Message API Demo
      </Typography>

      <div className="space-y-4">
        {/* Thread Info */}
        <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded">
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            Current Thread: {chatStream.threadId || 'None'}
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            Thread Name: {chatStream.threadName || 'None'}
          </Typography>
        </div>

        {/* Message Content */}
        <div>
          <label className="block text-sm font-medium mb-2">Message Content *</label>
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Enter your message..."
            rows={3}
            className="w-full"
          />
        </div>

        {/* Reply To Message ID */}
        <div>
          <label className="block text-sm font-medium mb-2">Reply To Message ID (Optional)</label>
          <Input
            value={replyToMessageId}
            onChange={(e) => setReplyToMessageId(e.target.value)}
            placeholder="Enter message ID to reply to..."
            className="w-full"
          />
        </div>

        {/* Message ID for Modify */}
        <div>
          <label className="block text-sm font-medium mb-2">Message ID to Modify (Optional)</label>
          <Input
            value={messageId}
            onChange={(e) => setMessageId(e.target.value)}
            placeholder="Enter message ID to modify..."
            className="w-full"
          />
        </div>

        {/* Attachment */}
        <div>
          <label className="block text-sm font-medium mb-2">Attachment (Optional)</label>
          <div className="flex gap-2">
            <select
              value={attachmentType}
              onChange={(e) => setAttachmentType(e.target.value as 'file' | 'image' | '')}
              className="px-3 py-2 border rounded"
            >
              <option value="">No Attachment</option>
              <option value="file">File</option>
              <option value="image">Image</option>
            </select>
            <Input
              value={attachmentId}
              onChange={(e) => setAttachmentId(e.target.value)}
              placeholder="Enter file/image ID..."
              className="flex-1"
              disabled={!attachmentType}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={handleSendMessage}
            disabled={!content.trim() || chatStream.isLoading}
            className="flex-1"
          >
            {chatStream.isLoading ? 'Sending...' : 'Send with DTO'}
          </Button>
          <Button
            onClick={handleSendSimpleMessage}
            disabled={!content.trim() || chatStream.isLoading}
            variant="outline"
            className="flex-1"
          >
            Send Simple
          </Button>
        </div>

        {/* Create Thread Button */}
        <Button
          onClick={() => chatStream.createNewThread('Demo Thread')}
          disabled={chatStream.isCreatingThread}
          variant="outline"
          className="w-full"
        >
          {chatStream.isCreatingThread ? 'Creating...' : 'Create New Thread'}
        </Button>

        {/* Status */}
        {chatStream.error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded p-3">
            <Typography variant="body2" className="text-red-700 dark:text-red-300">
              Error: {chatStream.error}
            </Typography>
          </div>
        )}

        {chatStream.streamError && (
          <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded p-3">
            <Typography variant="body2" className="text-orange-700 dark:text-orange-300">
              Stream Error: {chatStream.streamError.message}
            </Typography>
            {chatStream.streamError.retryContent && (
              <Button
                onClick={chatStream.retryLastMessage}
                size="sm"
                className="mt-2"
              >
                Retry
              </Button>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

export default ChatMessageDemo;
