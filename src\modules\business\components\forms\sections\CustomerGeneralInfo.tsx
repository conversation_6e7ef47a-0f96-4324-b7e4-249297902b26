import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Chip,
  Avatar,
  Icon,
  Button,
  Input,
  FormItem,
  Form,
  CollapsibleCard,
  IconCard,
  PhoneInputWithCountry,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { NotificationUtil } from '@/shared/utils/notification';
import { CustomerDetailData } from './types';
<<<<<<< HEAD
import { useUpdateCustomerBasicInfo, useUpdateCustomerInformation } from '../../../hooks/useCustomerQuery';
=======
import { useUpdateCustomerBasicInfo } from '../../../hooks/useCustomerQuery';
import { UpdateCustomerBasicInfoDto } from '../../../services/customer.service';
>>>>>>> develop
import {
  UpdateCustomerBasicInfoDto
} from '../../../services/customer.service';
import {
  UpdateCustomerBasicInfoFormValues,
  createUpdateCustomerBasicInfoSchema
} from '../../../schemas/customer.schema';

interface CustomerGeneralInfoProps {
  customer: CustomerDetailData;
}

/**
 * Utility function để parse số điện thoại quốc tế
 * @param phoneNumber Số điện thoại có thể có format +840793355880
 * @returns Object chứa country code và phone number
 */
const parseInternationalPhone = (phoneNumber: string) => {
  if (!phoneNumber) {
    return { countryCode: 'VN', phoneNumber: '', isValid: false };
  }

  // Kiểm tra xem có phải số điện thoại hợp lệ không
  const cleanPhone = phoneNumber.replace(/[^\d+]/g, '');

  // Nếu bắt đầu bằng +84 (Việt Nam)
  if (cleanPhone.startsWith('+84')) {
    const localNumber = cleanPhone.substring(3);
    // Kiểm tra số điện thoại Việt Nam hợp lệ (9 chữ số sau +84)
    const isValid = /^[3-9][0-9]{8}$/.test(localNumber);
    return { countryCode: 'VN', phoneNumber: localNumber, isValid };
  }

  // Nếu bắt đầu bằng +1 (US)
  if (cleanPhone.startsWith('+1')) {
    const localNumber = cleanPhone.substring(2);
    const isValid = /^[0-9]{10}$/.test(localNumber);
    return { countryCode: 'US', phoneNumber: localNumber, isValid };
  }

  // Nếu bắt đầu bằng +86 (China)
  if (cleanPhone.startsWith('+86')) {
    const localNumber = cleanPhone.substring(3);
    const isValid = /^[0-9]{11}$/.test(localNumber);
    return { countryCode: 'CN', phoneNumber: localNumber, isValid };
  }

  // Nếu không có dấu + và là số Việt Nam (bắt đầu bằng 0)
  if (cleanPhone.startsWith('0') && cleanPhone.length === 10) {
    const localNumber = cleanPhone.substring(1);
    const isValid = /^[3-9][0-9]{8}$/.test(localNumber);
    return { countryCode: 'VN', phoneNumber: localNumber, isValid };
  }

  // Mặc định trả về Việt Nam nếu không parse được
  return { countryCode: 'VN', phoneNumber: cleanPhone, isValid: false };
};

/**
 * Component hiển thị thông tin chung của khách hàng
 */
const CustomerGeneralInfo: React.FC<CustomerGeneralInfoProps> = ({ customer }) => {
  const { t } = useTranslation(['business', 'common']);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Hooks - tắt tất cả thông báo từ hooks để tránh duplicate
  const { formRef: formErrorsRef, setFormErrors } = useFormErrors<UpdateCustomerBasicInfoFormValues>();
  const updateBasicInfoMutation = useUpdateCustomerBasicInfo({
    showSuccessNotification: false, // Tắt thông báo success từ hook
    showErrorNotification: false,   // Xử lý error trong component
    showWarningNotification: false  // Tắt thông báo warning từ file upload
  });

  // State
  const [formData, setFormData] = useState<CustomerDetailData>(customer);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [newTag, setNewTag] = useState('');
  // State cho avatar file và preview URL
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreviewUrl, setAvatarPreviewUrl] = useState<string | null>(null);

  // Derived state
  const isSaving = updateBasicInfoMutation.isPending;

  // Effect để cập nhật state khi customer prop thay đổi
  useEffect(() => {
    setFormData(customer);
  }, [customer]);

  // Cleanup avatar preview URL khi component unmount
  useEffect(() => {
    return () => {
      if (avatarPreviewUrl) {
        URL.revokeObjectURL(avatarPreviewUrl);
      }
    };
  }, [avatarPreviewUrl]);

  // Reset avatarPreviewUrl khi customer prop thay đổi (từ server)
  useEffect(() => {
    if (avatarPreviewUrl) {
      URL.revokeObjectURL(avatarPreviewUrl);
      setAvatarPreviewUrl(null);
    }
  }, [customer.avatar]);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN');
    } catch {
      return dateString;
    }
  };

  // Get status chip variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'blocked':
        return 'danger';
      default:
        return 'default';
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Hoạt động';
      case 'inactive':
        return 'Không hoạt động';
      case 'blocked':
        return 'Bị khóa';
      default:
        return status;
    }
  };

  // Handle input change
  const handleInputChange = (field: keyof CustomerDetailData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle add tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()],
      }));
      setNewTag('');
    }
  };

  // Handle remove tag
  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || [],
    }));
  };



  // Handle avatar upload
  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      NotificationUtil.error({
        message: 'Vui lòng chọn file hình ảnh'
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      NotificationUtil.error({
        message: 'Kích thước file không được vượt quá 5MB'
      });
      return;
    }

    setIsUploadingAvatar(true);

    try {
      // Cleanup previous preview URL if exists
      if (avatarPreviewUrl) {
        URL.revokeObjectURL(avatarPreviewUrl);
      }

      // Create new preview URL
      const previewUrl = URL.createObjectURL(file);
      setAvatarPreviewUrl(previewUrl);

      // KHÔNG cập nhật formData.avatar để tránh Form component override
      // Avatar sẽ được quản lý riêng biệt thông qua avatarPreviewUrl

      // Store file for later upload
      setAvatarFile(file);

      console.log('Avatar selected:', file.name);
      console.log('Preview URL created:', previewUrl);
      console.log('Avatar will be displayed from avatarPreviewUrl, not formData');
    } catch (error) {
      console.error('Error processing avatar:', error);
      NotificationUtil.error({
        message: 'Có lỗi xảy ra khi xử lý avatar'
      });
    } finally {
      setIsUploadingAvatar(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle avatar click
  const handleAvatarClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle save
  const handleSave = async () => {
    try {
      // Prepare avatar file data if available
      let avatarFileData = undefined;
      if (avatarFile) {
        avatarFileData = {
          fileName: avatarFile.name,
          mimeType: avatarFile.type
        };
      }

      // Chỉ sử dụng API /basic-info với trường tags
      const dataToValidate: Record<string, unknown> = {};

      if (formData.name?.trim()) {
        dataToValidate['name'] = formData.name.trim();
      }

      if (formData.phone?.trim()) {
        dataToValidate['phone'] = formData.phone.trim();
      }

      if (formData.email?.trim()) {
        dataToValidate['email'] = {
          primary: formData.email.trim(),
          secondary: ''
        };
      }

      if (formData.address?.trim()) {
        dataToValidate['address'] = formData.address.trim();
      }

      // Thêm tags vào API /basic-info
      if (formData.tags) {
        dataToValidate['tags'] = formData.tags;
      }

      if (avatarFileData) {
        dataToValidate['avatarFile'] = avatarFileData;
      }

      const validatedData = basicInfoSchema.parse(dataToValidate);

      const basicMutationParams = {
        id: parseInt(formData.id),
        data: validatedData as UpdateCustomerBasicInfoDto,
        ...(avatarFile && { avatarFile })
      };

      const response = await updateBasicInfoMutation.mutateAsync(basicMutationParams);

      // Update local state on success
      setFormData(prev => {
        const updatedData: Partial<CustomerDetailData> = {
          ...prev,
          name: formData.name,
          phone: formData.phone,
          email: formData.email,
        };

        // Chỉ thêm address nếu có giá trị
        if (formData.address) {
          updatedData.address = formData.address;
        }

        // Chỉ thêm tags nếu có giá trị
        if (formData.tags) {
          updatedData.tags = formData.tags;
        }

        // Update avatar với priority: avatarUpload > avatar > prev.avatar
        const responseResult = response.result as unknown as Record<string, unknown>;
        if (responseResult?.['avatarUpload'] && typeof responseResult['avatarUpload'] === 'object') {
          const avatarUpload = responseResult['avatarUpload'] as Record<string, unknown>;
          if (avatarUpload['publicUrl'] && typeof avatarUpload['publicUrl'] === 'string') {
            updatedData.avatar = avatarUpload['publicUrl'];
            console.log('Avatar updated from server:', avatarUpload['publicUrl']);
          }
        } else if (responseResult?.['avatar'] && typeof responseResult['avatar'] === 'string') {
          updatedData.avatar = responseResult['avatar'];
          console.log('Avatar updated from response:', responseResult['avatar']);
        } else if (prev.avatar) {
          updatedData.avatar = prev.avatar;
        }

        return updatedData as CustomerDetailData;
      });

      // Clear avatar file and preview URL after successful save
      setAvatarFile(null);
      if (avatarPreviewUrl) {
        URL.revokeObjectURL(avatarPreviewUrl);
        setAvatarPreviewUrl(null);
      }

      // Show single success notification
      NotificationUtil.success({
        message: 'Cập nhật thông tin khách hàng thành công'
      });

    } catch (error: unknown) {
      console.error('Error saving customer:', error);

      // Handle API errors from backend
      if (error && typeof error === 'object' && 'response' in error && error.response) {
        const axiosError = error as {
          response: {
            data?: {
              code?: number;
              message?: string;
              errors?: Record<string, string>;
            }
          };
        };

        const errorData = axiosError.response.data;

        // Handle specific error codes
        if (errorData?.code === 30187) {
          // Duplicate phone number error
          setFormErrors({
            phone: errorData.message || 'Số điện thoại đã tồn tại trong hệ thống'
          });
          NotificationUtil.error({
            message: 'Số điện thoại đã tồn tại trong hệ thống'
          });
          return;
        }

        // Handle field-specific errors
        if (errorData?.errors) {
          setFormErrors(errorData.errors);
          NotificationUtil.error({
            message: 'Vui lòng kiểm tra lại thông tin đã nhập'
          });
          return;
        }

        // Handle general error message
        if (errorData?.message) {
          NotificationUtil.error({
            message: errorData.message
          });
          return;
        }
      }

      // Handle Zod validation errors
      if (error && typeof error === 'object' && 'errors' in error) {
        const zodError = error as { errors: Array<{ message: string; path: string[]; code: string }> };
        const formErrors: Record<string, string> = {};

        zodError.errors.forEach((err) => {
          // Handle nested paths like ['email', 'primary']
          if (err.path.length > 0) {
            const mainField = err.path[0];

            // Map nested email errors to the main email field
            if (mainField === 'email') {
              formErrors['email'] = err.message;
            } else if (typeof mainField === 'string') {
              formErrors[mainField] = err.message;
            }
          }
        });

        setFormErrors(formErrors);

        // Show notification for validation errors
        NotificationUtil.error({
          message: 'Vui lòng kiểm tra lại thông tin đã nhập'
        });
      } else {
        // Handle other types of errors
        NotificationUtil.error({
          message: 'Có lỗi xảy ra khi lưu thông tin khách hàng'
        });
      }
    }
  };

  // Handle form submit
  const handleSubmit = async () => {
    // Trigger save when form is submitted
    await handleSave();
  };

  // Debug avatar src
  const avatarSrc = avatarPreviewUrl || formData.avatar || `https://i.pravatar.cc/150?img=${formData.id}`;
  console.log('Avatar render - Preview URL:', avatarPreviewUrl);
  console.log('Avatar render - Form data avatar:', formData.avatar);
  console.log('Avatar render - Final src:', avatarSrc);

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('business:customer.detail.generalInfo')}
          </Typography>
          <Chip variant={getStatusVariant(formData.status)} size="sm">
            {getStatusText(formData.status)}
          </Chip>
        </div>
      }
      defaultOpen={true}
    >
      <div>
        <Form ref={formErrorsRef} onSubmit={handleSubmit} defaultValues={formData}>
          <div className="space-y-6">
            {/* Avatar và thông tin cơ bản */}
            <div className="flex items-start space-x-4">
              <div className="relative flex flex-col items-center">
                <div className="relative">
                  {/* Debug info - removed process.env check to avoid browser error */}
                  <Avatar
                    src={avatarSrc}
                    alt={formData.name}
                    size="3xl"
                    className={`${isUploadingAvatar ? 'opacity-50' : ''} transition-opacity`}
                    onLoad={() => {
                      console.log('Avatar loaded successfully:', avatarSrc);
                    }}
                    onError={(e) => {
                      console.error('Avatar failed to load:', avatarSrc, e);
                    }}
                  />

                  {/* Loading spinner */}
                  {isUploadingAvatar && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                    </div>
                  )}

                </div>

                {/* Upload button - căn giữa với avatar */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAvatarClick}
                  disabled={isUploadingAvatar}
                  className="mt-3"
                >
                  <Icon name="upload" size="sm" className="mr-1" />
                  {isUploadingAvatar ? 'Đang tải...' : 'Thay đổi'}
                </Button>

                {/* Hidden file input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                  className="hidden"
                />
              </div>

              <div className="flex-1">
                <Typography variant="body2" className="text-muted mb-2">
                  ID: {formData.id}
                </Typography>
              </div>
            </div>

            {/* Form fields */}
            <div className="grid grid-cols-1 gap-4">
              <FormItem label={t('business:common.form.name')} name="name" required>
                <Input
                  value={formData.name}
                  onChange={e => handleInputChange('name', e.target.value)}
                  placeholder={t('business:customer.form.namePlaceholder')}
                  fullWidth
                />
              </FormItem>

              <FormItem label={t('business:common.form.email')} name="email">
                <Input
                  type="email"
                  value={formData.email}
                  onChange={e => handleInputChange('email', e.target.value)}
                  placeholder={t('business:customer.form.emailPlaceholder')}
                  fullWidth
                />
              </FormItem>

              <FormItem label={t('business:common.form.phone')} name="phone">
                <PhoneInputWithCountry
                  value={formData.phone || ''}
                  onChange={(internationalPhone) => {
                    // Validate phone number trước khi lưu
                    const parsed = parseInternationalPhone(internationalPhone);
                    if (parsed.isValid || !internationalPhone) {
                      handleInputChange('phone', internationalPhone);
                    } else {
                      // Vẫn lưu để user có thể chỉnh sửa, nhưng log warning
                      console.warn('Invalid phone number format:', internationalPhone, 'Parsed:', parsed);
                      handleInputChange('phone', internationalPhone);
                    }
                  }}
                  placeholder={t('business:customer.form.phonePlaceholder')}
                  fullWidth
                  defaultCountry="VN"
                />
              </FormItem>

              <FormItem label={t('business:common.form.address')} name="address">
                <Input
                  value={formData.address}
                  onChange={e => handleInputChange('address', e.target.value)}
                  placeholder={t('business:customer.form.addressPlaceholder')}
                  fullWidth
                />
              </FormItem>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem label={t('business:customer.detail.customerSince')} name="customerSince">
                  <Input value={formatDate(formData.customerSince)} fullWidth disabled={true} />
                </FormItem>

                <FormItem label={t('business:common.form.tags')} name="tags">
                  <div className="space-y-2">
                    {/* Existing tags */}
                    <div className="flex flex-wrap gap-2">
                      {formData.tags && formData.tags.length > 0 ? (
                        formData.tags.map((tag, index) => (
                          <Chip key={index} size="sm" closable onClose={() => handleRemoveTag(tag)}>
                            {tag}
                          </Chip>
                        ))
                      ) : (
                        <Typography variant="body2" className="text-muted">
                          {t('business:customer.detail.noData')}
                        </Typography>
                      )}
                    </div>

                    {/* Add new tag */}
                    <div className="flex gap-2 items-center">
                      <Input
                        value={newTag}
                        onChange={e => setNewTag(e.target.value)}
                        onKeyDown={e => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleAddTag();
                          }
                        }}
                        placeholder="Thêm tag mới..."
                        className="flex-1"
                        fullWidth
                      />
                    </div>
                  </div>
                </FormItem>
              </div>
            </div>

            {/* Save button */}
            <div className="flex justify-end">
              <IconCard
                icon="check"
                onClick={handleSave}
                disabled={isSaving}
                variant="primary"
                title={t('common:save')}
                isLoading={isSaving}
                size="md"
              />
            </div>
          </div>
        </Form>
      </div>
    </CollapsibleCard>
  );
};

export default CustomerGeneralInfo;
