/**
 * Chat API Service
 * Xử lý tất cả REST API calls cho chat system
 */

import { apiClient } from '@/shared/api';
import {
  ChatMessageRequest,
  ChatMessageResponse,
  ChatContentBlock,
  ChatAttachmentContext,
  StopRunResponse,
  ContentBlock,
  CreateThreadRequest,
  CreateThreadResponse,
  GetThreadsQuery,
  GetThreadsResponse,
  GetMessagesQuery,
  GetMessagesResponseData,
} from '@/shared/types/chat-streaming.types';

/**
 * Chat API Service Class
 */
export class ChatApiService {
  private baseUrl: string;
  private timeout: number;
  private debug: boolean;

  constructor(baseUrl: string, timeout: number = 30000, debug: boolean = false) {
    this.baseUrl = baseUrl;
    this.timeout = timeout;
    this.debug = debug;
  }

  /**
   * Log debug information
   */
  private log(message: string, data?: unknown): void {
    if (this.debug) {
      console.log(`[ChatApiService] ${message}`, data || '');
    }
  }

  /**
   * Tạo thread chat mới
   * Endpoint: POST /v1/user/chat/threads
   */
  async createThread(name?: string): Promise<CreateThreadResponse> {
    const threadName = name || `New Chat ${Date.now()}`;

    this.log('Creating thread', { name: threadName });

    const requestBody: CreateThreadRequest = {
      name: threadName
    };

    try {
      const response = await apiClient.post<CreateThreadResponse>(
        '/user/chat/threads',
        requestBody,
        {
          timeout: this.timeout
        }
      );

      this.log('Thread created successfully', response.result);

      // apiClient trả về ApiResponseDto<CreateThreadResponse>
      // response.result chính là CreateThreadResponse
      return response.result;
    } catch (error) {
      this.log('Failed to create thread', error);
      throw this.handleApiError(error, 'Failed to create thread');
    }
  }

  /**
   * Lấy danh sách threads
   * Endpoint: GET /v1/user/chat/threads
   */
  async getThreads(query?: GetThreadsQuery): Promise<GetThreadsResponse> {
    console.log('[ChatApiService] Getting threads API call', { query, timestamp: Date.now() });
    this.log('Getting threads', query);

    // Tạo query parameters
    const params = new URLSearchParams();
    if (query?.page) params.append('page', query.page.toString());
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.sortBy) params.append('sortBy', query.sortBy);
    if (query?.sortDirection) params.append('sortDirection', query.sortDirection);
    if (query?.search) params.append('search', query.search);

    const url = `/user/chat/threads${params.toString() ? `?${params.toString()}` : ''}`;

    try {
      const response = await apiClient.get<GetThreadsResponse>(url, {
        timeout: this.timeout
      });

      this.log('Threads retrieved successfully', response.result);

      // apiClient trả về ApiResponseDto<GetThreadsResponse>
      // response.result chính là GetThreadsResponse
      return response.result;
    } catch (error) {
      this.log('Failed to get threads', error);
      throw this.handleApiError(error, 'Failed to get threads');
    }
  }

  /**
   * Lấy lịch sử tin nhắn trong thread
   * Endpoint: GET /v1/user/chat/threads/{threadId}/messages
   */
  async getMessages(
    threadId: string,
    query?: GetMessagesQuery
  ): Promise<GetMessagesResponseData> {
    console.log('[ChatApiService] Getting messages API call', { threadId, query, timestamp: Date.now() });
    this.log('Getting messages', { threadId, query });

    // Validate threadId
    if (!threadId || threadId.trim() === '') {
      throw new Error('Thread ID is required');
    }

    // Tạo query parameters với default values
    const params = new URLSearchParams();
    if (query?.page) params.append('page', query.page.toString());
    if (query?.limit) params.append('limit', query.limit.toString());
    if (query?.sortBy) params.append('sortBy', query.sortBy);
    if (query?.sortDirection) params.append('sortDirection', query.sortDirection);
    if (query?.role) params.append('role', query.role);

    const url = `/user/chat/threads/${threadId}/messages${params.toString() ? `?${params.toString()}` : ''}`;

    try {
      const response = await apiClient.get<GetMessagesResponseData>(url, {
        timeout: this.timeout
      });

      // apiClient trả về ApiResponseDto<GetMessagesResponse>
      // response.result chính là GetMessagesResponse
      return response.result;
    } catch (error) {
      this.log('Failed to get messages', { threadId, error });
      throw this.handleApiError(error, 'Failed to get messages');
    }
  }

  /**
   * Gửi tin nhắn chat - API chuẩn duy nhất
   * Endpoint: POST /v1/chat/message
   *
   * Hỗ trợ cả legacy string và ChatMessageRequest
   */
  async sendMessage(
    contentOrRequest: string | ChatMessageRequest,
    threadId?: string,
    alwaysApproveToolCall: boolean = false
  ): Promise<ChatMessageResponse> {
    let requestBody: ChatMessageRequest;

    // Kiểm tra nếu là string (legacy method)
    if (typeof contentOrRequest === 'string') {
      this.log('Sending message (legacy)', { content: contentOrRequest, threadId, alwaysApproveToolCall });

      // Tạo ChatMessageRequest từ string
      requestBody = {
        contentBlocks: [{ type: 'text', content: contentOrRequest }],
        threadId: threadId!,
        alwaysApproveToolCall
      };
    } else {
      // Nếu là object (ChatMessageRequest)
      this.log('Sending message with ChatMessageRequest', contentOrRequest);
      requestBody = contentOrRequest;
    }

    try {
      const response = await apiClient.post<ChatMessageResponse>(
        '/user/chat/message',
        requestBody,
        {
          timeout: this.timeout
        }
      );

      this.log('Message sent successfully', response.result);

      // apiClient trả về ApiResponseDto<ChatMessageResponse>
      // response.result chính là ChatMessageResponse
      return response.result;
    } catch (error) {
      this.log('Failed to send message', error);
      throw this.handleApiError(error, 'Failed to send message');
    }
  }

  /**
   * Gửi tin nhắn text đơn giản (helper method)
   */
  async sendTextMessage(
    content: string,
    threadId: string,
    alwaysApproveToolCall: boolean = false
  ): Promise<ChatMessageResponse> {
    this.log('Sending text message', { content, threadId, alwaysApproveToolCall });

    const request: ChatMessageRequest = {
      contentBlocks: [{ type: 'text', content }],
      threadId,
      alwaysApproveToolCall
    };

    return this.sendMessage(request);
  }

  /**
   * Reply tin nhắn (helper method)
   */
  async replyToMessage(
    content: string,
    threadId: string,
    replyToMessageId: string
  ): Promise<ChatMessageResponse> {
    this.log('Replying to message', { content, threadId, replyToMessageId });

    const request: ChatMessageRequest = {
      contentBlocks: [{ type: 'text', content }],
      threadId,
      replyToMessageId
    };

    return this.sendMessage(request);
  }

  /**
   * Modify tin nhắn hiện có (helper method)
   */
  async modifyMessage(
    content: string,
    threadId: string,
    messageId: string
  ): Promise<ChatMessageResponse> {
    this.log('Modifying message', { content, threadId, messageId });

    const request: ChatMessageRequest = {
      contentBlocks: [{ type: 'text', content }],
      threadId,
      messageId
    };

    return this.sendMessage(request);
  }

  /**
   * Gửi tin nhắn với attachments (helper method)
   */
  async sendMessageWithAttachments(
    content: string,
    threadId: string,
    attachments: { type: 'file' | 'image'; id: string }[]
  ): Promise<ChatMessageResponse> {
    this.log('Sending message with attachments', { content, threadId, attachments });

    const contentBlocks: ChatContentBlock[] = [
      { type: 'text', content }
    ];

    const attachmentContext: ChatAttachmentContext[] = [];

    // Thêm attachment blocks và context
    attachments.forEach(attachment => {
      if (attachment.type === 'file') {
        contentBlocks.push({ type: 'file', fileId: attachment.id });
        attachmentContext.push({ type: 'file', fileId: attachment.id });
      } else if (attachment.type === 'image') {
        contentBlocks.push({ type: 'image', imageId: attachment.id });
        attachmentContext.push({ type: 'image', imageId: attachment.id });
      }
    });

    const request: ChatMessageRequest = {
      contentBlocks,
      threadId,
      attachmentContext
    };

    return this.sendMessage(request);
  }

  /**
   * Approve tool call (helper method)
   */
  async approveToolCall(
    threadId: string,
    decision: 'yes' | 'no' | 'always'
  ): Promise<ChatMessageResponse> {
    this.log('Approving tool call', { threadId, decision });

    const request: ChatMessageRequest = {
      contentBlocks: [{ type: 'tool_call_decision', decision }],
      threadId,
      alwaysApproveToolCall: false
    };

    return this.sendMessage(request);
  }

  /**
   * Dừng chat run
   * Endpoint: DELETE /v1/chat/runs/{runId}
   */
  async stopRun(runId: string): Promise<StopRunResponse> {
    this.log('Stopping run', { runId });

    try {
      const response = await apiClient.delete<StopRunResponse>(
        `/user/chat/runs/${runId}`,
        {
          timeout: this.timeout
        }
      );

      this.log('Run stopped successfully', response.result);
      
      // apiClient trả về ApiResponseDto<StopRunResponse>
      // response.result chính là StopRunResponse
      return response.result;
    } catch (error) {
      this.log('Failed to stop run', error);
      throw this.handleApiError(error, 'Failed to stop run');
    }
  }

  /**
   * Xử lý lỗi API và tạo error message phù hợp
   */
  private handleApiError(error: unknown, defaultMessage: string): Error {
    if (error && typeof error === 'object' && 'response' in error) {
      const apiError = error as { response?: { data?: { message?: string } } };
      if (apiError.response?.data?.message) {
        return new Error(apiError.response.data.message);
      }
    }

    if (error instanceof Error && error.message) {
      return new Error(error.message);
    }

    return new Error(defaultMessage);
  }

  /**
   * Cập nhật base URL
   */
  updateBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
    this.log('Base URL updated', { baseUrl });
  }

  /**
   * Cập nhật timeout
   */
  updateTimeout(timeout: number): void {
    this.timeout = timeout;
    this.log('Timeout updated', { timeout });
  }

  /**
   * Cập nhật debug mode
   */
  updateDebug(debug: boolean): void {
    this.debug = debug;
    this.log('Debug mode updated', { debug });
  }

  /**
   * Lấy thông tin cấu hình hiện tại
   */
  getConfig(): { baseUrl: string; timeout: number; debug: boolean } {
    return {
      baseUrl: this.baseUrl,
      timeout: this.timeout,
      debug: this.debug
    };
  }
}
