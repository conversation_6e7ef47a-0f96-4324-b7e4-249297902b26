# SSE Authentication Debug Guide

Hướng dẫn debug SSE authentication với token.

## ✅ Đã sửa

1. **SSE Service Constructor**: Thêm `getAuthToken` parameter
2. **Token Handling**: Hỗ trợ cả sync và async token getter
3. **URL Building**: Thêm token vào query parameter
4. **useChatStream**: <PERSON><PERSON><PERSON><PERSON><PERSON> `getAuthToken` từ config

## 🔍 Debug Flow

### 1. Check getAuthToken Function
```javascript
// Trong browser console
const config = {
  getAuthToken: () => localStorage.getItem('authToken') || ''
};
console.log('Auth token:', config.getAuthToken());
```

### 2. Check SSE URL Construction
```
[ChatSSEService] SSE URL: {url: "https://v2.redai.vn/api/v1/chat/stream/events/thread-123/run-456?token=***"}
```

### 3. Expected SSE Connection
```
[ChatSSEService] Creating EventSource with URL: {...}
[ChatSSEService] SSE connection opened successfully
```

### 4. No More 401 Errors
```
// Trước đây (lỗi):
{
  "code": 9999,
  "message": "Authorization token not found.",
  "path": "/v1/chat/stream/events/...",
  "requestId": "..."
}

// Bây giờ (thành công):
[SSE] 🔥 RAW MESSAGE RECEIVED: {data: "...", type: "message"}
```

## 🚨 Potential Issues

### Issue 1: getAuthToken Returns Empty
```javascript
// Check trong useChatStream config
const config = {
  getAuthToken: () => {
    const token = localStorage.getItem('authToken');
    console.log('Getting auth token:', token ? 'Found' : 'Not found');
    return token || '';
  }
};
```

### Issue 2: Token Format Issues
```javascript
// Đảm bảo token không có Bearer prefix
const getAuthToken = () => {
  const token = localStorage.getItem('authToken');
  // Remove 'Bearer ' prefix if exists
  return token?.replace(/^Bearer\s+/, '') || '';
};
```

### Issue 3: URL Encoding Issues
```javascript
// Token được encode trong URL
const url = `${baseUrl}/events/${threadId}/${runId}?token=${encodeURIComponent(token)}`;
```

## 🔧 Debug Commands

### 1. Test Auth Token
```javascript
// Trong browser console
const token = localStorage.getItem('authToken');
console.log('Token exists:', !!token);
console.log('Token length:', token?.length);
console.log('Token preview:', token?.substring(0, 20) + '...');
```

### 2. Test SSE URL
```javascript
// Check SSE service
const sseService = window.sseServiceRef?.current;
if (sseService) {
  console.log('SSE service exists');
  // Manual connect test
  sseService.connect('test-thread', 'test-run');
}
```

### 3. Test Manual SSE Connection
```javascript
// Test EventSource manually
const token = localStorage.getItem('authToken');
const url = `https://v2.redai.vn/api/v1/chat/stream/events/test-thread/test-run?token=${encodeURIComponent(token)}`;
const eventSource = new EventSource(url);

eventSource.onopen = () => console.log('Manual SSE opened');
eventSource.onerror = (e) => console.error('Manual SSE error:', e);
eventSource.onmessage = (e) => console.log('Manual SSE message:', e.data);
```

## 📋 Checklist

- [ ] `getAuthToken` function returns valid token
- [ ] Token không có 'Bearer ' prefix
- [ ] SSE URL có token parameter
- [ ] EventSource connection thành công
- [ ] Không còn 401 errors
- [ ] Nhận được SSE events

## 🎯 Expected Log Sequence

1. **Get Auth Token**:
   - `Getting auth token: Found`

2. **Build SSE URL**:
   - `SSE URL: {url: "...?token=***"}`

3. **Connect EventSource**:
   - `Creating EventSource with URL: {...}`
   - `SSE connection opened successfully`

4. **Receive Events**:
   - `🔥 RAW MESSAGE RECEIVED: {...}`
   - `🎯 Routing event: "stream_text_token"`

## 🔍 Common Auth Issues

1. **Token not found**: Check localStorage key name
2. **Token expired**: Check token validity
3. **Token format**: Remove Bearer prefix
4. **URL encoding**: Ensure proper encodeURIComponent
5. **CORS issues**: Check server CORS settings

## 🛠️ Quick Fixes

### Fix 1: Check Token Storage
```javascript
// Check all possible token keys
const keys = ['authToken', 'token', 'accessToken', 'jwt'];
keys.forEach(key => {
  const value = localStorage.getItem(key);
  console.log(`${key}:`, value ? 'Found' : 'Not found');
});
```

### Fix 2: Manual Token Test
```javascript
// Set token manually for testing
localStorage.setItem('authToken', 'your-test-token-here');
```

### Fix 3: Check Network Tab
- Open DevTools → Network tab
- Look for SSE connection request
- Check if token is in URL
- Check response status (should be 200, not 401)

Với những thay đổi này, SSE connection sẽ có auth token và không còn bị lỗi 401!
