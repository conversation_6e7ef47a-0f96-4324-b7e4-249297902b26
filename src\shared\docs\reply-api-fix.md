# Reply API Fix - Sử dụng replyToMessageId thay vì ghép content

Tài liệu này mô tả việc sửa lỗi reply logic để sử dụng đúng API mới.

## ❌ Vấn đề trước đây

### 1. Ghép content thay vì sử dụng replyToMessageId
```typescript
// ❌ SAI - Ghép nội dung reply vào content
let finalMessage = messageToSend;
if (replyContext) {
  const replyContent = typeof replyContext.content === 'string'
    ? replyContext.content
    : 'Previous message';

  const replyId = replyMessage.id ? ` (ID: ${replyMessage.id})` : '';
  finalMessage = `[Replying to: "${replyContent.substring(0, 100)}${replyContent.length > 100 ? '...' : ''}"${replyId}]\n\n${messageToSend}`;
}

await chatStream.sendMessage(finalMessage);
```

### 2. Request body sai format
```json
{
  "alwaysApproveToolCall": false,
  "contentBlocks": [
    {
      "type": "text",
      "content": "[Replying to: \"Hello! How can I assist you today?\" (ID: c9270ac4-9d14-462e-88ed-ad25932fbb41)]\n\nbạn là ai"
    }
  ],
  "threadId": "681b4d34-6ea6-492b-b96b-1d181c3a93c2"
}
```

## ✅ Giải pháp đã sửa

### 1. Sử dụng replyToMessageId riêng biệt
```typescript
// ✅ ĐÚNG - Sử dụng replyToMessageId
if (replyContext && replyContext.id && chatStream.threadId) {
  const replyRequest = {
    contentBlocks: [{ type: 'text' as const, content: messageToSend }],
    threadId: chatStream.threadId,
    replyToMessageId: replyContext.id,
    alwaysApproveToolCall: false
  };
  
  await chatStream.sendMessage(replyRequest);
} else {
  // Gửi tin nhắn thường
  await chatStream.sendMessage(messageToSend);
}
```

### 2. Request body đúng format
```json
{
  "contentBlocks": [
    {
      "type": "text",
      "content": "bạn là ai"
    }
  ],
  "threadId": "681b4d34-6ea6-492b-b96b-1d181c3a93c2",
  "replyToMessageId": "c9270ac4-9d14-462e-88ed-ad25932fbb41",
  "alwaysApproveToolCall": false
}
```

## 🔍 So sánh Before/After

| Aspect | Before (❌) | After (✅) |
|--------|-------------|-----------|
| **Content** | Ghép reply content vào message | Chỉ gửi message hiện tại |
| **Reply Info** | Trong content string | Trong `replyToMessageId` field |
| **API Compliance** | Không tuân thủ API mới | Tuân thủ ChatMessageRequest |
| **Server Processing** | Server phải parse content | Server nhận structured data |
| **UI Display** | Hiển thị duplicate content | Hiển thị clean reply structure |

## 🎯 Benefits của giải pháp mới

### 1. **Clean Content**
- Message content chỉ chứa nội dung thực tế
- Không có duplicate reply context
- Dễ đọc và xử lý

### 2. **Structured Data**
- `replyToMessageId` riêng biệt
- Server có thể xử lý reply logic đúng cách
- Hỗ trợ nested replies và threading

### 3. **API Compliance**
- Tuân thủ `ChatMessageRequest` interface
- Tương thích với tất cả API endpoints
- Hỗ trợ future features

### 4. **Better UX**
- Reply indicator hiển thị đúng
- Không có content duplication
- Consistent với messaging apps khác

## 🔧 Implementation Details

### 1. Type Safety
```typescript
// Đảm bảo type safety với 'as const'
contentBlocks: [{ type: 'text' as const, content: messageToSend }]
```

### 2. Null Checks
```typescript
// Kiểm tra tất cả required fields
if (replyContext && replyContext.id && chatStream.threadId) {
  // Send reply
} else {
  // Send regular message
}
```

### 3. Debug Logging
```typescript
console.log('[ChatInputBox] 📤 Sending reply message:', {
  content: messageToSend,
  replyToMessageId: replyContext.id,
  threadId: chatStream.threadId
});
```

## 🚀 Expected Flow

### 1. User clicks Reply
- `replyMessage` state được set
- `ReplyPreview` component hiển thị

### 2. User types message
- Chỉ type nội dung reply
- Không có prefix hay context

### 3. User sends message
- Check có `replyContext.id` không
- Tạo `ChatMessageRequest` với `replyToMessageId`
- Gửi qua `chatStream.sendMessage()`

### 4. Server receives
- Nhận structured request
- Process reply logic
- Return proper response với reply metadata

### 5. UI updates
- Message hiển thị với reply indicator
- Clean content, không duplicate
- Proper threading structure

## 🔍 Testing

### Test Cases
1. **Regular Message**: Không có reply context
2. **Reply Message**: Có `replyToMessageId`
3. **Missing ID**: Reply context nhưng không có ID
4. **Missing ThreadId**: Không có threadId

### Expected Results
```typescript
// Regular message
{
  contentBlocks: [{ type: "text", content: "Hello" }],
  threadId: "thread-123"
}

// Reply message
{
  contentBlocks: [{ type: "text", content: "Thanks" }],
  threadId: "thread-123",
  replyToMessageId: "msg-456"
}
```

Với fix này, reply functionality sẽ hoạt động đúng theo API specification và không còn duplicate content!
