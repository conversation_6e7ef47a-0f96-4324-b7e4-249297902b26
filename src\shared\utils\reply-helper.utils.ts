/**
 * Utility functions để xử lý reply messages với API mới
 */

import { ChatMessage } from '@/shared/types/chat-streaming.types';

/**
 * Tìm reply content từ danh sách messages
 * @param replyToMessageId - ID của message được reply
 * @param messages - <PERSON>h sách tất cả messages
 * @returns Reply content hoặc undefined nếu không tìm thấy
 */
export function findReplyContent(
  replyToMessageId: string,
  messages: ChatMessage[]
): string | undefined {
  // Tìm message theo messageId (từ API) hoặc id (local ID) để hỗ trợ cả history và current messages
  const replyMessage = messages.find(msg =>
    msg.messageId === replyToMessageId || msg.id === replyToMessageId
  );

  if (!replyMessage) {
    return undefined;
  }

  // Lấy text content từ message
  return typeof replyMessage.content === 'string'
    ? replyMessage.content
    : String(replyMessage.content);
}

/**
 * Tạo reply info object từ metadata
 * @param message - ChatMessage có thể chứa reply info
 * @param allMessages - Danh sách tất cả messages để tìm reply content
 * @param historyMessages - Danh sách history messages (optional)
 * @returns Reply info object hoặc undefined
 */
export function extractReplyInfo(
  message: ChatMessage,
  allMessages?: ChatMessage[],
  historyMessages?: ChatMessage[]
): { replyToMessageId: string; replyContent?: string } | undefined {
  const replyToMessageId = message.metadata?.replyToMessageId;

  if (!replyToMessageId) {
    return undefined;
  }

  // Ưu tiên sử dụng replyContent từ metadata (từ API)
  let replyContent = message.metadata?.replyContent;

  // Nếu không có trong metadata, tìm từ allMessages và historyMessages
  if (!replyContent) {
    // Tìm trong allMessages trước
    if (allMessages) {
      replyContent = findReplyContent(replyToMessageId, allMessages);
    }

    // Nếu vẫn không tìm thấy, tìm trong historyMessages
    if (!replyContent && historyMessages) {
      replyContent = findReplyContent(replyToMessageId, historyMessages);
    }

    // Nếu vẫn không tìm thấy, set placeholder để vẫn hiển thị ReplyIndicator
    if (!replyContent) {
      replyContent = 'Tin nhắn gốc không tìm thấy';
    }
  }

  return {
    replyToMessageId,
    replyContent
  };
}

/**
 * Kiểm tra xem message có phải là reply không
 * @param message - ChatMessage cần kiểm tra
 * @returns true nếu là reply message
 */
export function isReplyMessage(message: ChatMessage): boolean {
  return !!(message.metadata?.replyToMessageId);
}

/**
 * Kiểm tra xem message có phải là modify không
 * @param message - ChatMessage cần kiểm tra
 * @returns true nếu là modify message
 */
export function isModifyMessage(message: ChatMessage): boolean {
  return !!(message.metadata?.modifiedMessageId);
}

/**
 * Lấy thông tin về message được modify
 * @param message - ChatMessage có thể chứa modify info
 * @returns Modify info hoặc undefined
 */
export function extractModifyInfo(
  message: ChatMessage
): { modifiedMessageId: string; updatedAt?: Date } | undefined {
  const modifiedMessageId = message.metadata?.modifiedMessageId;
  
  if (!modifiedMessageId) {
    return undefined;
  }

  return {
    modifiedMessageId,
    updatedAt: message.metadata?.updatedAt
  };
}

/**
 * Format reply content để hiển thị (truncate nếu quá dài)
 * @param content - Reply content
 * @param maxLength - Độ dài tối đa (default: 100)
 * @returns Formatted reply content
 */
export function formatReplyContent(content: string, maxLength: number = 100): string {
  if (!content) {
    return '';
  }

  // Loại bỏ line breaks và extra spaces
  const cleanContent = content.replace(/\s+/g, ' ').trim();
  
  if (cleanContent.length <= maxLength) {
    return cleanContent;
  }

  return cleanContent.substring(0, maxLength - 3) + '...';
}

/**
 * Tạo reply preview text cho UI
 * @param replyContent - Nội dung reply
 * @param senderName - Tên người gửi (optional)
 * @returns Preview text
 */
export function createReplyPreview(
  replyContent: string,
  senderName?: string
): string {
  const formattedContent = formatReplyContent(replyContent, 80);
  
  if (senderName) {
    return `${senderName}: ${formattedContent}`;
  }
  
  return formattedContent;
}

/**
 * Validate reply data
 * @param replyInfo - Reply info object
 * @returns Validation result
 */
export function validateReplyInfo(
  replyInfo: { replyToMessageId: string; replyContent?: string }
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!replyInfo.replyToMessageId) {
    errors.push('replyToMessageId is required');
  }

  if (replyInfo.replyToMessageId && typeof replyInfo.replyToMessageId !== 'string') {
    errors.push('replyToMessageId must be a string');
  }

  if (replyInfo.replyContent && typeof replyInfo.replyContent !== 'string') {
    errors.push('replyContent must be a string');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Helper để tạo reply message từ existing message
 * @param originalMessage - Message gốc
 * @param replyContent - Nội dung reply
 * @param threadId - Thread ID
 * @returns Reply message object (chưa có ID)
 */
export function createReplyFromMessage(
  originalMessage: ChatMessage,
  replyContent: string,
  threadId: string
): Omit<ChatMessage, 'id'> {
  return {
    content: replyContent,
    sender: 'user', // Reply thường từ user
    timestamp: new Date(),
    status: 'pending' as any,
    threadId,
    metadata: {
      replyToMessageId: originalMessage.id,
      replyContent: typeof originalMessage.content === 'string' 
        ? originalMessage.content 
        : String(originalMessage.content)
    }
  };
}

/**
 * Constants cho reply system
 */
export const REPLY_CONSTANTS = {
  MAX_REPLY_CONTENT_LENGTH: 100,
  MAX_PREVIEW_LENGTH: 80,
  REPLY_INDICATOR: '↳',
  MODIFY_INDICATOR: '✏️'
} as const;
