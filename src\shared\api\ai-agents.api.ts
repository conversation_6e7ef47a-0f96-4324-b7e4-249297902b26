import { apiClient } from './axios';
import { AgentSimpleListResult, AgentSimpleQueryParams } from '@/shared/types/ai-agents.types';
import {
  ChatMessageRequest,
  ChatMessageResponse,
  ChatContentBlock,
  ChatAttachmentContext,
  StopRunResponse
} from '@/shared/types/chat-streaming.types';

/**
 * API service cho AI Agents
 */
export class AIAgentsApi {
  /**
   * Lấy danh sách agents đơn giản
   */
  static async getSimpleAgents(params: AgentSimpleQueryParams = {}): Promise<AgentSimpleListResult> {
    // Set default values
    const defaultParams = {
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortDirection: 'DESC' as const,
      ...params
    };

    const response = await apiClient.get<AgentSimpleListResult>(
      '/user/agents/simple',
      { params: defaultParams }
    );

    return response.result;
  }

  /**
   * <PERSON><PERSON><PERSON> tin nhắn chat - API chuẩn duy nhất
   * Endpoint: POST /v1/chat/message
   *
   * Hỗ trợ tất cả các tính năng:
   * - Text messages
   * - Reply messages (Facebook Messenger-style)
   * - Modify messages (với cascade delete)
   * - Attachments (file và image)
   * - Tool call decisions
   *
   * @param request - ChatMessageRequest với đầy đủ tính năng
   * @returns ChatMessageResponse với modification details
   */
  static async sendMessage(request: ChatMessageRequest): Promise<ChatMessageResponse> {
    const response = await apiClient.post<ChatMessageResponse>(
      '/chat/message',
      request
    );

    // apiClient trả về ApiResponseDto<ChatMessageResponse>
    // response.result chính là ChatMessageResponse
    return response.result;
  }

  /**
   * Gửi tin nhắn text đơn giản (helper method)
   * @param content - Nội dung tin nhắn
   * @param threadId - ID của thread
   * @param alwaysApproveToolCall - Tự động approve tool calls
   */
  static async sendTextMessage(
    content: string,
    threadId: string,
    alwaysApproveToolCall: boolean = false
  ): Promise<ChatMessageResponse> {
    const request: ChatMessageRequest = {
      contentBlocks: [{ type: 'text', content }],
      threadId,
      alwaysApproveToolCall
    };

    return this.sendMessage(request);
  }

  /**
   * Reply tin nhắn (helper method)
   * @param content - Nội dung reply
   * @param threadId - ID của thread
   * @param replyToMessageId - ID của message được reply
   */
  static async replyToMessage(
    content: string,
    threadId: string,
    replyToMessageId: string
  ): Promise<ChatMessageResponse> {
    const request: ChatMessageRequest = {
      contentBlocks: [{ type: 'text', content }],
      threadId,
      replyToMessageId
    };

    return this.sendMessage(request);
  }

  /**
   * Modify tin nhắn hiện có (helper method)
   * @param content - Nội dung mới
   * @param threadId - ID của thread
   * @param messageId - ID của message cần modify
   */
  static async modifyMessage(
    content: string,
    threadId: string,
    messageId: string
  ): Promise<ChatMessageResponse> {
    const request: ChatMessageRequest = {
      contentBlocks: [{ type: 'text', content }],
      threadId,
      messageId
    };

    return this.sendMessage(request);
  }

  /**
   * Gửi tin nhắn với attachments (helper method)
   * @param content - Nội dung tin nhắn
   * @param threadId - ID của thread
   * @param attachments - Danh sách attachments
   */
  static async sendMessageWithAttachments(
    content: string,
    threadId: string,
    attachments: { type: 'file' | 'image'; id: string }[]
  ): Promise<ChatMessageResponse> {
    const contentBlocks: ChatContentBlock[] = [
      { type: 'text', content }
    ];

    const attachmentContext: ChatAttachmentContext[] = [];

    // Thêm attachment blocks và context
    attachments.forEach(attachment => {
      if (attachment.type === 'file') {
        contentBlocks.push({ type: 'file', fileId: attachment.id });
        attachmentContext.push({ type: 'file', fileId: attachment.id });
      } else if (attachment.type === 'image') {
        contentBlocks.push({ type: 'image', imageId: attachment.id });
        attachmentContext.push({ type: 'image', imageId: attachment.id });
      }
    });

    const request: ChatMessageRequest = {
      contentBlocks,
      threadId,
      attachmentContext
    };

    return this.sendMessage(request);
  }

  /**
   * Approve tool call (helper method)
   * @param threadId - ID của thread
   * @param decision - yes hoặc no
   */
  static async approveToolCall(
    threadId: string,
    decision: 'yes' | 'no'
  ): Promise<ChatMessageResponse> {
    const request: ChatMessageRequest = {
      contentBlocks: [{ type: 'tool_call_decision', decision }],
      threadId,
      alwaysApproveToolCall: false
    };

    return this.sendMessage(request);
  }

  /**
   * Dừng chat run - API mới
   * Endpoint: DELETE /v1/chat/runs/{runId}
   */
  static async stopRun(runId: string): Promise<StopRunResponse> {
    const response = await apiClient.delete<StopRunResponse>(
      `/chat/runs/${runId}`
    );

    // Tương tự với StopRunResponse
    return response.result;
  }
}
