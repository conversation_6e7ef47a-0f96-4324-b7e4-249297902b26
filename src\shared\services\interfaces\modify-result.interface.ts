/**
 * Interface for message modification details
 * Used when modifying existing messages with cascade delete behavior
 */
export interface ModificationDetails {
  /**
   * ID of the message that was modified
   */
  modifiedMessageId: string;

  /**
   * Array of message IDs that were deleted due to cascade delete
   */
  deletedMessageIds: string[];

  /**
   * Total count of messages deleted
   */
  deletedMessagesCount: number;

  /**
   * Type of modification operation performed
   */
  operationType: 'modify_text' | 'modify_content' | 'modify_attachments';
}
