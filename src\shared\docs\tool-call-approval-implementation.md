# Tool Call Approval Implementation

Tài liệu này mô tả implementation của tool call approval system với sự kiện `tool_call_interrupt`.

## 🎯 **Overview**

Khi AI muốn gọi tool nhưng cần user approval, server sẽ gửi `tool_call_interrupt` event. System sẽ hiển thị form với 3 options để user quyết định:
- **Yes, Allow**: Approve tool call này
- **No, Deny**: Reject tool call này  
- **Always Allow**: Auto-approve future tool calls

## 🔧 **Implementation Details**

### **1. SSE Event Handler**

#### **ToolCallInterruptData Interface**
```typescript
export interface ToolCallInterruptData {
  role: string;
  toolName: string;
  toolDescription?: string;
  parameters?: Record<string, unknown>;
  threadId: string;
  runId?: string;
}
```

#### **SSE Callbacks Extension**
```typescript
export interface SSECallbacks {
  // ... existing callbacks
  onToolCallInterrupt?: (toolCallData: ToolCallInterruptData) => void;
}
```

#### **Event Handler**
```typescript
private handleToolCallInterruptEvent(event: MessageEvent): void {
  const data = eventData.data;
  const toolCallData: ToolCallInterruptData = {
    role: data?.role || '',
    toolName: data?.toolName || '',
    toolDescription: data?.toolDescription || '',
    parameters: data?.parameters || {},
    threadId: data?.threadId || this.currentThreadId || '',
    runId: data?.runId || this.currentRunId || ''
  };

  this.callbacks.onToolCallInterrupt?.(toolCallData);
}
```

### **2. useChatStream Integration**

#### **State Management**
```typescript
// Tool call interrupt state
const [toolCallInterrupt, setToolCallInterrupt] = useState<ToolCallInterruptData | null>(null);

// SSE callback
onToolCallInterrupt: (toolCallData) => {
  console.log('[useChatStream] ⚠️ Tool call interrupt:', toolCallData);
  setToolCallInterrupt(toolCallData);
},
```

#### **Approval Functions**
```typescript
const approveToolCall = useCallback(async (decision: 'yes' | 'no' | 'always') => {
  if (!toolCallInterrupt || !apiServiceRef.current) return;

  await apiServiceRef.current.approveToolCall(toolCallInterrupt.threadId, decision);
  setToolCallInterrupt(null);
}, [toolCallInterrupt]);

const dismissToolCallInterrupt = useCallback(() => {
  setToolCallInterrupt(null);
}, []);
```

### **3. ToolCallApprovalForm Component**

#### **Props Interface**
```typescript
export interface ToolCallApprovalFormProps {
  toolCallData: ToolCallInterruptData;
  onApprove: (decision: 'yes' | 'no' | 'always') => void;
  onDismiss: () => void;
  isLoading?: boolean;
}
```

#### **UI Features**
- **Modal overlay** với backdrop
- **Tool information** display (name, description, parameters)
- **3 decision buttons**:
  - ✅ Yes, Allow (green)
  - ❌ No, Deny (red)  
  - 🔄 Always Allow (blue)
- **Cancel button** (gray)
- **Loading states** với spinner
- **Dark mode support**

### **4. useToolCallApproval Hook**

#### **Features**
```typescript
export interface UseToolCallApprovalReturn {
  toolCallData: ToolCallInterruptData | null;
  isApproving: boolean;
  approvalError: string | null;
  showApprovalForm: (toolCallData: ToolCallInterruptData) => void;
  hideApprovalForm: () => void;
  handleApproval: (decision: 'yes' | 'no' | 'always') => Promise<void>;
  clearApprovalError: () => void;
}
```

#### **Error Handling**
- Catch approval errors
- Display error messages
- Retry mechanism
- Auto-dismiss on success

### **5. Integration Example**

#### **ChatWithToolApproval Component**
```typescript
const ChatWithToolApproval: React.FC = () => {
  const chatStream = useChatStream({ /* config */ });
  const toolApproval = useToolCallApproval({
    onApprove: async (decision) => {
      await chatStream.approveToolCall(decision);
    },
    onDismiss: () => {
      chatStream.dismissToolCallInterrupt();
    }
  });

  // Listen for tool call interrupts
  useEffect(() => {
    if (chatStream.toolCallInterrupt) {
      toolApproval.showApprovalForm(chatStream.toolCallInterrupt);
    }
  }, [chatStream.toolCallInterrupt]);

  return (
    <div>
      {/* Chat UI */}
      
      {/* Tool Call Approval Form */}
      {toolApproval.toolCallData && (
        <ToolCallApprovalForm
          toolCallData={toolApproval.toolCallData}
          onApprove={toolApproval.handleApproval}
          onDismiss={toolApproval.hideApprovalForm}
          isLoading={toolApproval.isApproving}
        />
      )}
    </div>
  );
};
```

## 🔄 **Event Flow**

### **Normal Flow**
```
1. User sends message
2. AI processes and wants to call tool
3. Server sends tool_call_interrupt event
4. Client shows ToolCallApprovalForm
5. User selects decision (yes/no/always)
6. Client calls approveToolCall API
7. Server continues with tool execution
8. Normal streaming resumes
```

### **SSE Event Format**
```json
{
  "event": "tool_call_interrupt",
  "data": {
    "role": "supervisor",
    "toolName": "web_search",
    "toolDescription": "Search the web for information",
    "parameters": {
      "query": "latest AI news",
      "limit": 10
    },
    "threadId": "thread-123",
    "runId": "run-456"
  },
  "timestamp": 1749824252530
}
```

### **API Request Format**
```json
{
  "contentBlocks": [
    {
      "type": "tool_call_decision",
      "decision": "yes"
    }
  ],
  "threadId": "thread-123",
  "alwaysApproveToolCall": false
}
```

## 🎨 **UI/UX Features**

### **Visual Design**
- **Modal overlay** với blur backdrop
- **Card-based layout** với rounded corners
- **Color-coded buttons**:
  - Green: Approve
  - Red: Deny
  - Blue: Always allow
  - Gray: Cancel
- **Loading states** với spinners
- **Responsive design** cho mobile

### **Accessibility**
- **Keyboard navigation** support
- **Screen reader** friendly
- **Focus management**
- **ARIA labels**

### **Dark Mode**
- **Automatic theme detection**
- **Consistent color scheme**
- **Proper contrast ratios**

## 🔧 **Configuration Options**

### **useChatStream Config**
```typescript
const chatStream = useChatStream({
  agentId: 'agent-123',
  alwaysApproveToolCall: false, // Set to true to skip approval
  getAuthToken: () => getToken(),
  debug: true
});
```

### **useToolCallApproval Config**
```typescript
const toolApproval = useToolCallApproval({
  autoDismissOnSuccess: true, // Auto hide form after approval
  onApprove: async (decision, toolCallData) => {
    // Custom approval logic
  },
  onDismiss: (toolCallData) => {
    // Custom dismiss logic
  }
});
```

## 🚨 **Error Handling**

### **Common Errors**
1. **Network errors** during approval
2. **Invalid tool call data**
3. **Missing thread/run IDs**
4. **API timeout**

### **Error Recovery**
- **Retry mechanism** cho failed approvals
- **Error messages** với clear descriptions
- **Fallback options** cho network issues
- **State cleanup** on errors

## 🎯 **Testing**

### **Test Cases**
1. **Normal approval flow** (yes/no/always)
2. **Dismiss without approval**
3. **Network error handling**
4. **Multiple tool calls** in sequence
5. **Dark mode rendering**
6. **Mobile responsiveness**

### **Mock Data**
```typescript
const mockToolCallData: ToolCallInterruptData = {
  role: 'supervisor',
  toolName: 'web_search',
  toolDescription: 'Search the web for information',
  parameters: { query: 'test query', limit: 5 },
  threadId: 'test-thread-123',
  runId: 'test-run-456'
};
```

## 🚀 **Usage**

### **Basic Integration**
```typescript
import { useChatStream } from '@/shared/hooks/common/useChatStream';
import { useToolCallApproval } from '@/shared/hooks/chat/useToolCallApproval';
import ToolCallApprovalForm from '@/shared/components/chat/ToolCallApprovalForm';

// Use in your chat component
const { toolCallInterrupt, approveToolCall, dismissToolCallInterrupt } = useChatStream(config);
```

### **Advanced Integration**
```typescript
// Use ChatWithToolApproval component directly
import ChatWithToolApproval from '@/shared/components/chat/ChatWithToolApproval';

<ChatWithToolApproval
  agentId="agent-123"
  threadId="thread-456"
  getAuthToken={() => getAuthToken()}
/>
```

Với implementation này, system sẽ handle tool call approvals một cách smooth và user-friendly!
