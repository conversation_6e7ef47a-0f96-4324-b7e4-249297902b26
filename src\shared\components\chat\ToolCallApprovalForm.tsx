/**
 * ToolCallApprovalForm Component
 * Inline message để approve/reject tool calls khi có tool_call_interrupt event
 * Hỗ trợ responsive design và đa ngôn ngữ
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { ToolCallInterruptData } from '@/shared/services/chat-sse.service';

export interface ToolCallApprovalFormProps {
  /**
   * Tool call data từ interrupt event
   */
  toolCallData: ToolCallInterruptData;

  /**
   * Callback khi user chọn decision
   */
  onApprove: (decision: 'yes' | 'no') => void;

  /**
   * Loading state khi đang gửi approval
   */
  isLoading?: boolean;
}

/**
 * ToolCallApprovalForm Component
 */
const ToolCallApprovalForm: React.FC<ToolCallApprovalFormProps> = ({
  toolCallData,
  onApprove,
  isLoading = false
}) => {
  const { t } = useTranslation(['chat']);

  const handleDecision = (decision: 'yes' | 'no') => {
    onApprove(decision);
  };

  return (
    <div className="mx-2 sm:mx-4 my-2">
      <div className="rounded-lg p-3 sm:p-4">
        {/* Header with Actions */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-3 lg:gap-4 mb-3">
          {/* Title Section */}
          <div className="flex items-start sm:items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center flex-shrink-0">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="text-sm sm:text-base font-semibold text-gray-900 dark:text-gray-100 truncate">
                {t('chat:toolCall.approval.title')}
              </h3>
              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 break-words">
                {t('chat:toolCall.approval.subtitle')}: <span className="font-medium text-primary">{toolCallData.toolName}</span>
              </p>
            </div>
          </div>

          {/* Actions Section */}
          <div className="flex items-center gap-2 lg:gap-3 flex-shrink-0">
            {/* Action Buttons */}
            <button
              onClick={() => handleDecision('yes')}
              disabled={isLoading}
              className="flex-1 lg:flex-none lg:min-w-[40px] bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 disabled:bg-green-400 dark:disabled:bg-green-500 text-white font-medium py-2 px-3 rounded-lg text-sm transition-colors flex items-center justify-center gap-1 shadow-sm dark:shadow-none focus:outline-none focus:ring-2 focus:ring-green-500/50 dark:focus:ring-green-400/60"
              title={t('chat:toolCall.approval.tooltips.allow')}
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </>
              )}
            </button>
            <button
              onClick={() => handleDecision('no')}
              disabled={isLoading}
              className="flex-1 lg:flex-none lg:min-w-[40px] bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700 disabled:bg-red-400 dark:disabled:bg-red-500 text-white font-medium py-2 px-3 rounded-lg text-sm transition-colors flex items-center justify-center gap-1 shadow-sm dark:shadow-none focus:outline-none focus:ring-2 focus:ring-red-500/50 dark:focus:ring-red-400/60"
              title={t('chat:toolCall.approval.tooltips.deny')}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolCallApprovalForm;
