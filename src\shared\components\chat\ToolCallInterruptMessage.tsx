/**
 * ToolCallInterruptMessage Component
 * Hiển thị tool call interrupt trong lịch sử chat (read-only)
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/shared/components/common';

export interface ToolCallInterruptMessageProps {
  /**
   * Tool call data từ lịch sử
   */
  toolCallData?: {
    role?: string;
    toolName?: string;
    toolDescription?: string;
    parameters?: Record<string, unknown>;
  } | null;

  /**
   * Timestamp của interrupt
   */
  timestamp?: Date;

  /**
   * Custom className
   */
  className?: string;

  /**
   * C<PERSON> hiển thị chi tiết parameters không
   */
  showDetails?: boolean;
}

/**
 * ToolCallInterruptMessage Component
 */
const ToolCallInterruptMessage: React.FC<ToolCallInterruptMessageProps> = ({
  toolCallData,
  timestamp,
  className = '',
  showDetails = false
}) => {
  const { t } = useTranslation(['chat']);

  return (
    <div className={`p-4 rounded-lg border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/20 ${className}`}>
      {/* Header */}
      <div className="flex items-center gap-2 mb-2">
        <Icon 
          name="exclamation-triangle" 
          size="sm" 
          className="text-orange-600 dark:text-orange-400"
        />
        <span className="text-sm font-medium text-orange-800 dark:text-orange-200">
          {t('chat.toolCallInterruptHistory', 'Đã yêu cầu xác nhận tool call')}
        </span>
        {timestamp && (
          <span className="text-xs text-orange-600 dark:text-orange-400 ml-auto">
            {timestamp.toLocaleTimeString()}
          </span>
        )}
      </div>

      {/* Tool Info */}
      {toolCallData && (
        <div className="space-y-2">
          {/* Tool Name */}
          {toolCallData.toolName && (
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium text-orange-700 dark:text-orange-300">
                {t('chat.toolName', 'Tool:')}
              </span>
              <code className="text-xs bg-orange-100 dark:bg-orange-800/50 px-2 py-1 rounded text-orange-800 dark:text-orange-200">
                {toolCallData.toolName}
              </code>
            </div>
          )}

          {/* Tool Description */}
          {toolCallData.toolDescription && (
            <div className="text-xs text-orange-700 dark:text-orange-300">
              {toolCallData.toolDescription}
            </div>
          )}

          {/* Role */}
          {toolCallData.role && (
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium text-orange-700 dark:text-orange-300">
                {t('chat.role', 'Role:')}
              </span>
              <span className="text-xs text-orange-600 dark:text-orange-400">
                {toolCallData.role}
              </span>
            </div>
          )}

          {/* Parameters (if showDetails is true) */}
          {showDetails && toolCallData.parameters && Object.keys(toolCallData.parameters).length > 0 && (
            <div className="mt-2">
              <div className="text-xs font-medium text-orange-700 dark:text-orange-300 mb-1">
                {t('chat.parameters', 'Parameters:')}
              </div>
              <div className="bg-orange-100 dark:bg-orange-800/50 rounded p-2">
                <pre className="text-xs text-orange-800 dark:text-orange-200 whitespace-pre-wrap">
                  {JSON.stringify(toolCallData.parameters, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Status indicator */}
      <div className="flex items-center gap-1 mt-2 pt-2 border-t border-orange-200 dark:border-orange-700">
        <Icon 
          name="clock" 
          size="xs" 
          className="text-orange-500 dark:text-orange-400"
        />
        <span className="text-xs text-orange-600 dark:text-orange-400">
          {t('chat.waitingForApproval', 'Đã chờ xác nhận')}
        </span>
      </div>
    </div>
  );
};

export default ToolCallInterruptMessage;
