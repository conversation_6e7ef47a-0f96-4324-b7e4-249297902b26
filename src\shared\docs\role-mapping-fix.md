# Role Mapping Fix - Unified AI Role Mapping

Tài liệu này mô tả fix cho vấn đề message splitting do role mapping không nhất quán.

## ❌ Vấn đề đã phát hiện

### Root Cause: Inconsistent Role Mapping

Từ SSE events:
```
stream_text_token: role="supervisor"
llm_stream_end: role="supervisor"  
message_created: role="assistant"
```

**Vấn đề**: `mapRoleToMessageSender` function map các roles khác nhau:
- `supervisor` → `supervisor` (MessageSender)
- `assistant` → `assistant` (MessageSender)

**Kết quả**: Logic role change detect "supervisor" ≠ "assistant" và tạo message mới, gây message splitting.

## ✅ Giải pháp đã implement

### Fix 1: Unified AI Role Mapping

**Trước đây**: Roles khác nhau cho AI
```typescript
// ❌ Mapping cũ - gây message splitting
export function mapRoleToMessageSender(role?: string): MessageSender {
  switch (role.toLowerCase()) {
    case 'supervisor':
      return 'supervisor'; // ← Khác với 'assistant'
    case 'worker':
      return 'worker';
    case 'user':
      return 'user';
    case 'assistant':
    case 'ai':
    default:
      return 'assistant'; // ← Khác với 'supervisor'
  }
}
```

**Sau khi sửa**: Tất cả AI roles map thành 'assistant'
```typescript
// ✅ Mapping mới - tránh message splitting
export function mapRoleToMessageSender(role?: string): MessageSender {
  switch (role.toLowerCase()) {
    case 'worker':
      return 'worker';
    case 'user':
      return 'user';
    case 'supervisor':
    case 'assistant':
    case 'ai':
    default:
      return 'assistant'; // ✅ Tất cả AI roles đều map thành 'assistant'
  }
}
```

### Fix 2: Enhanced Role Tracking in Metadata

**Vấn đề**: Mất thông tin về role gốc từ SSE

**Giải pháp**: Lưu role gốc trong metadata
```typescript
// ✅ Track role gốc trong metadata
const newMessage: ChatMessage = {
  id: uuidv4(),
  content: text,
  sender: mappedSender, // 'assistant' cho tất cả AI roles
  timestamp: new Date(),
  status: MessageStatus.STREAMING,
  threadId: threadId!,
  metadata: {
    ...(currentAgentId && { agentId: currentAgentId }),
    originalRole: role as any, // ✅ Lưu role gốc từ SSE (supervisor, assistant)
    streamingRole: role as any // ✅ Track role hiện tại đang stream
  }
};
```

### Fix 3: Dynamic Role Tracking During Streaming

**Vấn đề**: Không track role changes trong cùng message

**Giải pháp**: Update streamingRole khi có role change nhỏ
```typescript
// ✅ Update role trong cùng message
if (currentStreamingMessageRef.current.metadata) {
  (currentStreamingMessageRef.current.metadata as any).streamingRole = role;
}

setMessages(prev => prev.map(msg =>
  msg.id === currentStreamingMessageRef.current?.id
    ? {
        ...msg,
        content: currentStreamingMessageRef.current!.content,
        metadata: {
          ...currentStreamingMessageRef.current!.metadata,
          streamingRole: role as any
        }
      }
    : msg
));
```

## 🔍 Logic Flow

### Before Fix (Message Splitting)
```
Token 1: role="supervisor" → sender="supervisor" → Create Message 1
Token 2: role="assistant" → sender="ai" → Different sender! → Create Message 2
Result: 2 separate messages ❌
```

### After Fix (Single Message)
```
Token 1: role="supervisor" → sender="ai" → Create Message 1
Token 2: role="assistant" → sender="ai" → Same sender → Continue Message 1
Result: 1 continuous message ✅
```

### Role Information Preservation

| Information | Location | Purpose |
|-------------|----------|---------|
| **sender** | `message.sender` | UI logic, role change detection |
| **originalRole** | `message.metadata.originalRole` | Track first role from SSE |
| **streamingRole** | `message.metadata.streamingRole` | Track current streaming role |
| **messageCreatedRole** | `message.metadata.messageCreatedRole` | Track role from message_created event |

## 🎯 Expected Results

### 1. No Message Splitting
- All AI responses (supervisor, assistant) remain as single messages
- Only split between user ↔ AI interactions
- Smooth streaming experience

### 2. Role Information Preserved
- Original role from SSE preserved in metadata
- Current streaming role tracked dynamically
- Message created role from API preserved

### 3. Consistent Sender Mapping
- All AI roles → `sender: 'assistant'`
- Worker → `sender: 'worker'`
- User → `sender: 'user'`

## 🔧 Debug Information

### Check Role Mapping
```javascript
// Test role mapping
console.log(mapRoleToMessageSender('supervisor')); // Should be 'assistant'
console.log(mapRoleToMessageSender('assistant')); // Should be 'assistant'
console.log(mapRoleToMessageSender('worker')); // Should be 'worker'
console.log(mapRoleToMessageSender('user')); // Should be 'user'
```

### Check Message Metadata
```javascript
// Check role tracking in message
const message = messages[messages.length - 1];
console.log({
  sender: message.sender,
  originalRole: message.metadata?.originalRole,
  streamingRole: message.metadata?.streamingRole,
  messageCreatedRole: message.metadata?.messageCreatedRole
});
```

### Expected SSE Flow
```
1. stream_text_token: role="supervisor" → sender="assistant", originalRole="supervisor"
2. stream_text_token: role="supervisor" → sender="assistant", streamingRole="supervisor" (continue same message)
3. llm_stream_end: role="supervisor" → finalize message
4. message_created: role="assistant" → assign messageId, messageCreatedRole="assistant"
5. stream_session_end → cleanup
```

## 🚨 Troubleshooting

### Issue 1: Messages still splitting
- Check console for "🔄 ROLE CHANGE DETECTED"
- Verify `mapRoleToMessageSender` returns 'assistant' for all AI roles
- Check if role mapping is consistent

### Issue 2: Lost role information
- Check `originalRole` in message metadata
- Verify `streamingRole` is being updated
- Check `messageCreatedRole` from message_created event

### Issue 3: Inconsistent behavior
- Clear browser cache and reload
- Check if multiple versions of the function exist
- Verify imports are using updated version

## 🎯 Testing

### Test Cases
1. **Supervisor Only Response**: Should be 1 message with sender='assistant'
2. **Assistant Only Response**: Should be 1 message with sender='assistant'
3. **Mixed AI Response**: Should be 1 message with role tracking in metadata
4. **User-Assistant Conversation**: Should split between user and assistant

### Expected Behavior
- Single continuous AI messages
- Proper role tracking in metadata
- No unnecessary message splitting
- Clean streaming experience

Với fix này, tất cả AI roles sẽ được treat như cùng một sender, tránh message splitting không cần thiết!
