import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Icon, Tooltip, ReplyPreview } from '@/shared/components/common';
import { ReplyMessage } from '@/shared/components/common';
import FileUploadPreview, { UploadedFile } from './FileUploadPreview';
import UploadMenu from './UploadMenu';
import { useChatStream, UseChatStreamReturn } from '@/shared/hooks/common';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';
import useChatNotification from '@/shared/hooks/common/useChatNotification';
import { chatConfigService } from '@/shared/services';
import { toolApprovalSettingsService } from '@/shared/services/tool-approval-settings.service';

interface ChatInputBoxProps {
  onOpenMenu: () => void;
  onInputChange?: (text: string) => void; // Callback khi nội dung input thay đổi
  onKeywordDetected?: (keyword: string) => void; // Callback khi phát hiện keyword
  placeholder?: string;
  disabled?: boolean;
  chatInputRef?: React.RefObject<HTMLTextAreaElement>; // Ref cho textarea để focus từ bên ngoài
  // Chat streaming props
  chatStream?: UseChatStreamReturn; // Chat stream instance từ parent
  addNotification?: (type: 'success' | 'error' | 'warning' | 'info', message: string, duration?: number) => void;
  // Reply message props
  replyMessage?: ReplyMessage | null; // Tin nhắn được reply
  onClearReply?: () => void; // Callback để clear reply
  // Center notification props
  setCenterNotification?: (notification: { message: string; type?: 'info' | 'success' | 'warning' | 'error'; duration?: number; } | null) => void;
  // Edit message props
  editContent?: string | undefined; // Nội dung để edit
  editingMessageId?: string | null | undefined; // ID của message đang edit
  onEditComplete?: (() => void) | undefined; // Callback khi hoàn thành edit
  onEditCancel?: (() => void) | undefined; // Callback khi hủy edit
}

const ChatInputBox: React.FC<ChatInputBoxProps> = ({
  onOpenMenu,
  onInputChange,
  onKeywordDetected,
  placeholder = 'Nhập / để chọn nhanh menu...',
  disabled = false,
  chatInputRef,
  chatStream: externalChatStream,
  addNotification: externalAddNotification,
  replyMessage,
  onClearReply,
  setCenterNotification,
  editContent,
  editingMessageId,
  onEditComplete,
}) => {
  const { t } = useTranslation();
  const [message, setMessage] = useState('');
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isUploadMenuOpen, setIsUploadMenuOpen] = useState(false);

  const [isWebSearchActive, setIsWebSearchActive] = useState(false);
  const [isVoiceActive, setIsVoiceActive] = useState(false);

  // Edit mode state
  const lastEditContentRef = useRef<string | undefined>(undefined);
  const isInEditModeRef = useRef(false);
  const [chatInputWidth, setChatInputWidth] = useState<number | undefined>(undefined);
  const [isAutoApproveEnabled, setIsAutoApproveEnabled] = useState(false);
  const internalTextareaRef = useRef<HTMLTextAreaElement>(null);
  const textareaRef = chatInputRef || internalTextareaRef;
  const resizeTimeoutRef = useRef<NodeJS.Timeout>();

  // Use external chat stream and notification if provided, otherwise create new ones
  const { addNotification: localAddNotification } = useChatNotification();
  const { getToken } = useAuthCommon();
  const chatConfig = chatConfigService.getConfig();

  // Use external chatStream if provided, otherwise create new one
  const localChatStream = useChatStream({
    agentId: chatConfig.agentId,
    apiBaseUrl: chatConfig.apiBaseUrl,
    sseBaseUrl: chatConfig.sseBaseUrl,
    alwaysApproveToolCall: chatConfig.alwaysApproveToolCall,
    getAuthToken: () => getToken() || '',
    debug: chatConfig.debug
  });

  // Use external or local instances
  const chatStream = externalChatStream || localChatStream;
  const addNotification = externalAddNotification || localAddNotification;
  const chatInputContainerRef = useRef<HTMLDivElement>(null);

  // Load auto approve setting từ localStorage khi component mount
  useEffect(() => {
    const isEnabled = toolApprovalSettingsService.shouldAlwaysApprove();
    setIsAutoApproveEnabled(isEnabled);
    console.log('[ChatInputBox] Loaded auto approve setting:', isEnabled);
  }, []);

  // Handle edit content từ bên ngoài
  useEffect(() => {
    console.log('[ChatInputBox] editContent changed:', {
      editContent,
      editContentType: typeof editContent,
      editContentLength: editContent?.length,
      lastEditContent: lastEditContentRef.current,
      isInEditMode: isInEditModeRef.current,
      currentMessage: message
    });

    if (editContent !== undefined) {
      // Entering edit mode
      console.log('[ChatInputBox] ✅ Entering edit mode with content:', editContent);
      lastEditContentRef.current = editContent;
      isInEditModeRef.current = true;
      setMessage(editContent);

      // Focus vào textarea sau khi set content
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
          // Đặt cursor ở cuối text
          const length = editContent.length;
          textareaRef.current.setSelectionRange(length, length);
          console.log('[ChatInputBox] ✅ Focused textarea and set cursor position');
        }
      }, 0);
    } else if (editContent === undefined && isInEditModeRef.current) {
      // Exiting edit mode (cancel edit)
      console.log('[ChatInputBox] ✅ Exiting edit mode - clearing message');
      lastEditContentRef.current = undefined;
      isInEditModeRef.current = false;
      setMessage(''); // Clear message về trống
    }
  }, [editContent, textareaRef]);

  // Handle editingMessageId changes để clear message khi cancel edit
  useEffect(() => {
    console.log('[ChatInputBox] editingMessageId changed:', {
      editingMessageId,
      isInEditMode: isInEditModeRef.current,
      currentMessage: message
    });

    // Nếu editingMessageId thành null và đang trong edit mode → cancel edit
    if (editingMessageId === null && isInEditModeRef.current) {
      console.log('[ChatInputBox] 🚫 Cancel edit detected - clearing message');
      isInEditModeRef.current = false;
      lastEditContentRef.current = undefined;
      setMessage(''); // Clear message về trống
    }
  }, [editingMessageId]);

  // Auto-resize textarea based on content
  const adjustTextareaHeight = useCallback(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;

      // Save current scroll position
      const scrollTop = textarea.scrollTop;

      // Reset height to minimum to get accurate scrollHeight
      textarea.style.height = '44px';

      // Force reflow
      void textarea.offsetHeight;

      // Calculate new height based on content
      const scrollHeight = textarea.scrollHeight;
      const minHeight = 44; // Minimum height (1 line)
      const maxHeight = 120; // Maximum height (about 5 lines)

      // Set height within min/max bounds
      const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
      textarea.style.height = `${newHeight}px`;

      // Show scrollbar if content exceeds max height
      textarea.style.overflow = newHeight >= maxHeight ? 'auto' : 'hidden';

      // Restore scroll position if needed
      if (newHeight >= maxHeight) {
        textarea.scrollTop = scrollTop;
      }
    }
  }, [textareaRef]);

  // Debounced resize function
  const debouncedAdjustHeight = useCallback(() => {
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }
    resizeTimeoutRef.current = setTimeout(adjustTextareaHeight, 10);
  }, [adjustTextareaHeight]);

  // Adjust height when message changes
  useEffect(() => {
    adjustTextareaHeight();
  }, [message, adjustTextareaHeight]);

  // Initial setup và input event listener
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Set initial height
      textarea.style.height = '44px';
      textarea.style.minHeight = '44px';
      textarea.style.maxHeight = '120px';

      // Add input event listener để handle paste và other input methods
      const handleInput = () => {
        debouncedAdjustHeight();
      };

      textarea.addEventListener('input', handleInput);

      return () => {
        textarea.removeEventListener('input', handleInput);
        // Clear resize timeout
        if (resizeTimeoutRef.current) {
          clearTimeout(resizeTimeoutRef.current);
        }
      };
    }

    return undefined;
  }, [debouncedAdjustHeight, textareaRef]);

  // Update chat input width
  useEffect(() => {
    const updateWidth = () => {
      if (chatInputContainerRef.current) {
        const newWidth = chatInputContainerRef.current.offsetWidth;
        if (newWidth > 0 && Math.abs(newWidth - (chatInputWidth || 0)) > 5) {
          setChatInputWidth(newWidth);
        }
      }
    };

    const currentRef = chatInputContainerRef.current;
    if (currentRef) {
      updateWidth();

      window.addEventListener('resize', updateWidth);
      window.addEventListener('layout-resized', updateWidth);
      window.addEventListener('layout-resizing', updateWidth);

      const resizeObserver = new ResizeObserver(() => {
        requestAnimationFrame(updateWidth);
      });
      resizeObserver.observe(currentRef);

      return () => {
        window.removeEventListener('resize', updateWidth);
        window.removeEventListener('layout-resized', updateWidth);
        window.removeEventListener('layout-resizing', updateWidth);
        if (currentRef) {
          resizeObserver.unobserve(currentRef);
        }
        resizeObserver.disconnect();
      };
    }

    return undefined;
  }, [chatInputWidth]);

  // Xử lý sự kiện paste để hỗ trợ dán ảnh từ clipboard
  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      // Kiểm tra xem có đang focus vào textarea không
      if (document.activeElement !== textareaRef.current) return;

      console.log('[ChatInputBox] Paste event detected');

      // Kiểm tra xem clipboard có chứa ảnh không
      if (e.clipboardData && e.clipboardData.items) {
        const items = e.clipboardData.items;

        let imageItem = null;
        // Tìm item đầu tiên có type là image
        for (let i = 0; i < items.length; i++) {
          if (items[i]?.type?.indexOf('image') !== -1) {
            imageItem = items[i];
            break;
          }
        }

        // Nếu tìm thấy ảnh trong clipboard
        if (imageItem) {
          e.preventDefault(); // Ngăn không cho paste text vào textarea

          // Chuyển đổi thành file
          const file = imageItem.getAsFile();
          if (file) {
            console.log('[ChatInputBox] Pasted image from clipboard:', file.name, file.type);

            // Tạm thời disable browser notifications để debug
            if ('Notification' in window) {
              console.log('[ChatInputBox] Browser Notification API detected - temporarily disabled');
            }

            // Kiểm tra giới hạn số lượng file
            if (files.length >= 5) {
              addNotification('error', t('chat.maxFilesExceeded', { max: 5 }), 5000);
              return;
            }

            // Tạo tên file dựa trên thời gian hiện tại
            const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-');
            const fileName = `pasted-image-${timestamp}.png`;

            // Tạo file mới với tên đã đổi
            const renamedFile = new File([file], fileName, { type: file.type });

            // Xử lý file tương tự như khi upload từ máy tính
            const id = `clipboard-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
            const url = URL.createObjectURL(renamedFile);

            // Thêm file vào danh sách với trạng thái loading
            const newFile: UploadedFile = {
              id,
              name: fileName,
              type: renamedFile.type,
              url,
              thumbnail: url,
              isLoading: true,
            };

            setFiles(prev => [...prev, newFile]);

            // Bỏ thông báo khi paste image theo yêu cầu
            // addNotification('success', t('chat.imagePasted'), 3000);
            console.log('[ChatInputBox] Image pasted successfully - NO NOTIFICATION SHOWN');

            // Mô phỏng việc tải lên S3
            const uploadTime = Math.random() * 2000 + 1000; // 1-3 giây
            setTimeout(() => {
              setFiles(prevFiles => {
                return prevFiles.map(f => {
                  if (f.id === id) {
                    return { ...f, isLoading: false };
                  }
                  return f;
                });
              });
              console.log(`Clipboard image uploaded after ${uploadTime}ms`);
            }, uploadTime);
          }
        }
      }
    };

    // Thêm event listener cho sự kiện paste
    document.addEventListener('paste', handlePaste);

    // Cleanup
    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [files, addNotification, t, textareaRef]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // ✅ Kiểm tra tool call interrupt trước khi gửi
      if (chatStream.toolCallInterrupt) {
        console.log('[ChatInputBox] 🚫 Cannot send message via Enter - waiting for tool call approval');
        if (setCenterNotification) {
          setCenterNotification({
            message: 'Vui lòng xác nhận tool call trước khi gửi tin nhắn mới',
            type: 'warning',
            duration: 3000
          });
        }
        return;
      }
      handleSendMessage();
    }
  };

  const handleSendMessage = async () => {
    // ✅ KIỂM TRA TOOL CALL INTERRUPT - KHÔNG CHO PHÉP GỬI TIN NHẮN
    if (chatStream.toolCallInterrupt) {
      console.log('[ChatInputBox] 🚫 Cannot send message - waiting for tool call approval');
      if (setCenterNotification) {
        setCenterNotification({
          message: 'Vui lòng xác nhận tool call trước khi gửi tin nhắn mới',
          type: 'warning',
          duration: 3000
        });
      }
      return;
    }

    if (message.trim() && !disabled) {
      // Lưu message và reply context trước khi xóa
      const messageToSend = message.trim();
      const replyContext = replyMessage;

      // ✅ BƯỚC 1: XÓA INPUT NGAY LẬP TỨC
      setMessage('');
      setFiles([]);
      // Reset textarea height to minimum
      if (textareaRef.current) {
        textareaRef.current.style.height = '44px';
      }

      // Clear reply message sau khi gửi
      if (replyContext && onClearReply) {
        onClearReply();
      }

      // Clear edit content sau khi gửi (nếu đang trong edit mode)
      if (onEditComplete) {
        onEditComplete();
      }

      try {
        // Kiểm tra keyword trước khi gửi
        if (onKeywordDetected) {
          // Có thể thêm logic keyword detection ở đây nếu cần
          // onKeywordDetected(detectedKeyword);
        }

        console.log('isStreaming', chatStream.isStreaming);

        // Nếu đang gửi tin nhắn khác, dừng tin nhắn đó trước
        if (chatStream.isLoading) {
          await chatStream.stopStreaming();
        }

        // ✅ Kiểm tra xem có đang edit message không
        if (editingMessageId) {
          // Edit mode: Update message hiện có
          console.log('[ChatInputBox] 🔍 DEBUG - About to update message:', {
            messageId: editingMessageId,
            newContent: messageToSend,
            hasUpdateMethod: !!chatStream.updateMessage,
            chatStreamState: {
              isLoading: chatStream.isLoading,
              isStreaming: chatStream.isStreaming,
              threadId: chatStream.threadId
            }
          });

          // Gọi API update message (cần implement trong chatStream)
          if (chatStream.updateMessage) {
            await chatStream.updateMessage(editingMessageId, messageToSend);
          } else {
            console.warn('[ChatInputBox] updateMessage method not available in chatStream');
            // Fallback: Gửi tin nhắn mới
            await chatStream.sendMessage(messageToSend);
          }
        } else if (replyContext && replyContext.id && chatStream.threadId) {
          // Reply mode: Sử dụng API reply với replyToMessageId
          const replyRequest = {
            contentBlocks: [{ type: 'text' as const, content: messageToSend }],
            threadId: chatStream.threadId,
            replyToMessageId: replyContext.id,
            alwaysApproveToolCall: false
          };

          console.log('[ChatInputBox] 📤 Sending reply message:', {
            content: messageToSend,
            replyToMessageId: replyContext.id,
            threadId: chatStream.threadId
          });

          await chatStream.sendMessage(replyRequest);
        } else {
          // Normal mode: Gửi tin nhắn thường
          console.log('[ChatInputBox] 📤 Sending regular message:', messageToSend);
          await chatStream.sendMessage(messageToSend);
        }

      } catch (error) {
        console.error('Failed to send message:', error);
        addNotification('error', 'Không thể gửi tin nhắn', 5000);

        // Nếu có lỗi, khôi phục lại message và reply
        setMessage(messageToSend);
        // Note: Không khôi phục reply để tránh confusion
      }
    }
  };

  const handleStopSending = async () => {
    try {
      console.log('[ChatInputBox] 🛑 CANCEL BUTTON CLICKED - Starting stop process...', {
        isLoading: chatStream.isLoading,
        isStreaming: chatStream.isStreaming,
        isConnected: chatStream.isConnected,
        currentRunId: chatStream.currentRunId
      });

      await chatStream.stopStreaming();

      console.log('[ChatInputBox] ✅ Stop streaming completed successfully', {
        isLoading: chatStream.isLoading,
        isStreaming: chatStream.isStreaming,
        isConnected: chatStream.isConnected,
        currentRunId: chatStream.currentRunId
      });
    } catch (error) {
      console.error('[ChatInputBox] ❌ Failed to stop sending:', error);
    }
  };



  // Validate file types
  const isValidFileType = (file: File): boolean => {
    const allowedTypes = [
      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      // Documents
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
      'text/csv',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
      'application/json',
      'text/markdown',
      'application/x-jsonl',
    ];

    const isValid = allowedTypes.includes(file.type);
    console.log(`File ${file.name} type: ${file.type} is ${isValid ? 'valid' : 'invalid'}`);
    return isValid;
  };

  const handleUploadFromComputer = (fileList: FileList) => {
    console.log('handleUploadFromComputer called with', fileList.length, 'files');

    // Check if adding these files would exceed the limit
    if (files.length + fileList.length > 5) {
      console.log('Max files exceeded, showing notification');
      // Show error message or notification
      addNotification('error', t('chat.maxFilesExceeded', { max: 5 }), 5000);
      return;
    }

    // Đảm bảo menu đã đóng
    setIsUploadMenuOpen(false);

    try {
      // Convert FileList to array safely
      const fileArray = Array.from(fileList);
      const validFiles = fileArray.filter(isValidFileType);
      console.log('Valid files:', validFiles.length);

      // Always show warning if there are invalid files
      if (validFiles.length < fileArray.length) {
        console.log('Invalid files detected, showing notification');
        // Show warning about invalid files
        addNotification('warning', t('chat.invalidFileTypes'), 5000);
        console.log('Notification shown for invalid files');
      }

      // Process files one by one to avoid issues
      const newFiles: UploadedFile[] = [];

      // Process files one by one to avoid issues
      validFiles.forEach(file => {
        try {
          const id = `file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
          const url = URL.createObjectURL(file);

          // Create thumbnail for images
          let thumbnail = '';
          if (file.type.startsWith('image/')) {
            thumbnail = url;
          }

          // Thêm file với trạng thái đang loading
          newFiles.push({
            id,
            name: file.name,
            type: file.type,
            url,
            thumbnail,
            isLoading: true, // Đánh dấu file đang trong trạng thái loading
          });
        } catch (err) {
          console.error('Error processing file:', file.name, err);
        }
      });

      console.log('Adding new files:', newFiles.length);

      // Use a callback to ensure we're getting the latest state
      setFiles(prev => {
        const updatedFiles = [...prev, ...newFiles];
        console.log('Total files after update:', updatedFiles.length);
        return updatedFiles;
      });

      // Mô phỏng việc tải file lên S3 với thời gian ngẫu nhiên
      newFiles.forEach(file => {
        const uploadTime = Math.random() * 2000 + 1000; // Thời gian tải từ 1-3 giây
        setTimeout(() => {
          // Cập nhật trạng thái file sau khi tải lên "S3" hoàn tất
          setFiles(prevFiles => {
            return prevFiles.map(f => {
              if (f.id === file.id) {
                return { ...f, isLoading: false };
              }
              return f;
            });
          });
          console.log(`File ${file.name} uploaded successfully after ${uploadTime}ms`);
        }, uploadTime);
      });
    } catch (err) {
      console.error('Error handling files:', err);
      addNotification('error', t('chat.errorProcessingFiles'), 5000);
    }
  };

  const handleUploadFromGoogleDrive = () => {
    // Check if adding a file would exceed the limit
    if (files.length >= 5) {
      console.log('Max files exceeded for Google Drive, showing notification');
      // Show error message or notification
      addNotification('error', t('chat.maxFilesExceeded', { max: 5 }), 5000);
      return;
    }

    // Đảm bảo menu đã đóng
    setIsUploadMenuOpen(false);

    // This would be implemented with Google Picker API
    console.log('Upload from Google Drive');
    // For demo purposes, let's add a mock file
    const mockFile: UploadedFile = {
      id: `gdrive-${Date.now()}`,
      name: 'Google Drive Document.pdf',
      type: 'application/pdf',
      url: '#',
      isLoading: true, // Đánh dấu file đang trong trạng thái loading
    };

    // Thêm file vào danh sách với trạng thái loading
    setFiles(prev => [...prev, mockFile]);

    // Mô phỏng việc tải file từ Google Drive
    const uploadTime = Math.random() * 2000 + 1000; // Thời gian tải từ 1-3 giây
    setTimeout(() => {
      // Cập nhật trạng thái file sau khi tải hoàn tất
      setFiles(prevFiles => {
        return prevFiles.map(f => {
          if (f.id === mockFile.id) {
            return { ...f, isLoading: false };
          }
          return f;
        });
      });
      console.log(`File ${mockFile.name} uploaded from Google Drive after ${uploadTime}ms`);
    }, uploadTime);
  };

  // Xử lý chọn file từ thư viện
  const handleSelectFromLibrary = (selectedFiles: Array<{ id: string; name: string; type: 'knowledge' | 'media'; url?: string }>) => {
    console.log('handleSelectFromLibrary called with', selectedFiles.length, 'files');

    // Check if adding these files would exceed the limit
    if (files.length + selectedFiles.length > 5) {
      console.log('Max files exceeded, showing notification');
      addNotification('error', t('chat.maxFilesExceeded', { max: 5 }), 5000);
      return;
    }

    // Đảm bảo menu đã đóng
    setIsUploadMenuOpen(false);

    // Chuyển đổi file từ thư viện thành UploadedFile format
    const libraryFiles: UploadedFile[] = selectedFiles.map(file => ({
      id: `library-${file.type}-${file.id}`,
      name: file.name,
      type: file.type === 'knowledge' ? 'application/pdf' : 'image/jpeg', // Default type based on category
      url: file.url || '#',
      isLoading: false,
      source: file.type === 'knowledge' ? 'knowledge' : 'media', // Thêm source để phân biệt
    }));

    // Thêm các file đã chọn vào danh sách
    setFiles(prev => [...prev, ...libraryFiles]);

    console.log(`Added ${libraryFiles.length} files from library`);
  };

  const handleRemoveFile = (id: string) => {
    setFiles(files.filter(file => file.id !== id));
  };



  const handleWebSearch = () => {
    setIsWebSearchActive(!isWebSearchActive);
    // Implement web search logic here
  };

  const handleVoiceInput = () => {
    setIsVoiceActive(!isVoiceActive);

    // Hiệu ứng rung nhẹ cho button
    if (navigator.vibrate) {
      navigator.vibrate(100);
    }

    // Hiệu ứng thông báo bằng centerNotification
    if (setCenterNotification) {
      setCenterNotification({
        message: isVoiceActive ? 'Đã tắt voice input' : 'Đã bật voice input',
        type: 'info',
        duration: 2000
      });
    }

    // Auto tắt sau 3 giây nếu đang bật
    if (!isVoiceActive) {
      setTimeout(() => {
        setIsVoiceActive(false);
      }, 3000);
    }
  };

  // Handle toggle auto approve
  const handleToggleAutoApprove = () => {
    const newState = !isAutoApproveEnabled;
    setIsAutoApproveEnabled(newState);

    if (newState) {
      // Bật auto approve - lưu vào localStorage
      toolApprovalSettingsService.enableAlwaysApproveAllTools();
      console.log('[ChatInputBox] ✅ Auto approve enabled');
    } else {
      // Tắt auto approve - xóa khỏi localStorage
      toolApprovalSettingsService.disableAlwaysApproveAllTools();
      console.log('[ChatInputBox] ❌ Auto approve disabled');
    }
  };

  return (
    <div className="relative w-full" ref={chatInputContainerRef}>
      {/* Reply preview area */}
      {replyMessage && onClearReply && (
        <div className="mb-2">
          <ReplyPreview
            replyMessage={replyMessage}
            onCancel={onClearReply}
          />
        </div>
      )}

      {/* File preview area */}
      <FileUploadPreview files={files} onRemove={handleRemoveFile} />

      {/* Main input box */}
      <div className={`relative flex flex-col bg-white dark:bg-gray-800 rounded-xl shadow-lg w-full chat-input-box-container min-h-[100px] ${
        chatStream.toolCallInterrupt
          ? 'border-2 border-orange-300 dark:border-orange-600' // ✅ Visual indicator khi có tool call interrupt
          : ''
      }`}>
        {/* Text input area */}
        <div className="w-full px-3 py-3 min-h-[56px] flex items-start flex-shrink-0" style={{ paddingTop: '12px' }}>
          <textarea
            ref={textareaRef}
            value={message}
            onChange={e => {
              const newValue = e.target.value;
              setMessage(newValue);
              // Gọi callback onInputChange nếu được cung cấp
              if (onInputChange) {
                onInputChange(newValue);
              }
              // Auto-resize with debouncing
              debouncedAdjustHeight();
            }}
            onKeyDown={handleKeyDown}
            placeholder={
              chatStream.toolCallInterrupt
                ? 'Đang chờ xác nhận tool call...'
                : placeholder
            }
            disabled={disabled || !!chatStream.toolCallInterrupt} // ✅ Disable textarea khi có tool call interrupt
            className={`w-full bg-transparent border-0 focus:ring-0 focus:outline-none resize-none min-h-[44px] leading-6 custom-scrollbar ${
              chatStream.toolCallInterrupt
                ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed' // ✅ Style khi có tool call interrupt
                : 'dark:text-white text-gray-800'
            }`}
            rows={1}
            style={{
              minHeight: '44px',
              maxHeight: '120px',
              overflow: 'hidden',
              lineHeight: '1.5'
            }}
          />
        </div>

        {/* Action buttons row */}
        <div className="flex items-center px-2 py-2 space-x-1 h-[44px] flex-shrink-0">
          {/* Menu button */}
          <Tooltip content={t('common.menu')} position="top">
            <button
              onClick={onOpenMenu}
              className="p-2 w-10 h-10 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors flex-shrink-0"
              aria-label={t('common.menu')}
            >
              <Icon name="menu" size="md" />
            </button>
          </Tooltip>

          {/* Upload button */}
          <div className="relative flex-shrink-0">
            <Tooltip content={t('chat.uploadFile')} position="top">
              <button
                onClick={() => setIsUploadMenuOpen(!isUploadMenuOpen)}
                className="p-2 w-10 h-10 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                aria-label={t('chat.uploadFile')}
              >
                <Icon name="plus" size="md" />
              </button>
            </Tooltip>

            {/* Upload menu */}
            {isUploadMenuOpen && (
              <UploadMenu
                isOpen={isUploadMenuOpen}
                onClose={() => {
                  console.log('Closing upload menu');
                  setIsUploadMenuOpen(false);
                }}
                onUploadFromComputer={handleUploadFromComputer}
                onUploadFromGoogleDrive={handleUploadFromGoogleDrive}
                onSelectFromLibrary={handleSelectFromLibrary}
              />
            )}
          </div>

          {/* Web search button */}
          <Tooltip content={t('chat.webSearch')} position="top">
            <button
              onClick={handleWebSearch}
              className={`p-2 w-10 h-10 flex items-center justify-center rounded-full transition-colors ${isWebSearchActive
                  ? 'text-primary dark:text-primary-light'
                  : 'text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              aria-label={t('chat.webSearch')}
            >
              <Icon name="website" size="md" />
            </button>
          </Tooltip>

          {/* Auto approve switch */}
          <Tooltip content={isAutoApproveEnabled ? 'Tắt tự động phê duyệt tool' : 'Bật tự động phê duyệt tool'} position="top">
            <button
              onClick={handleToggleAutoApprove}
              className={`auto-approve-switch ${isAutoApproveEnabled ? 'enabled' : ''}`}
              aria-label={isAutoApproveEnabled ? 'Tắt tự động phê duyệt tool' : 'Bật tự động phê duyệt tool'}
            >
              {/* Switch toggle circle - đổi vị trí */}
              <div className="auto-approve-switch-circle" />
              {/* Auto text - đổi vị trí ngược với circle */}
              <span className="auto-approve-switch-text">
                Auto
              </span>
            </button>
          </Tooltip>

          <div className="flex-grow"></div>

          {/* Voice input button */}
          <Tooltip content={t('chat.voiceInput', 'Voice Input')} position="top">
            <button
              onClick={handleVoiceInput}
              className={`p-2 w-10 h-10 flex items-center justify-center rounded-full transition-all duration-300 transform ${isVoiceActive
                  ? 'text-red-500 bg-red-50 dark:bg-red-900/20 scale-110 animate-pulse'
                  : 'text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-105'
                }`}
              aria-label={t('chat.voiceInput', 'Voice Input')}
            >
              <Icon name="microphone" size="md" />
            </button>
          </Tooltip>

          {/* Send/Stop button */}
          <Tooltip
            content={
              chatStream.toolCallInterrupt
                ? 'Đang chờ xác nhận tool call'
                : (chatStream.isLoading || chatStream.isStreaming)
                  ? t('chat.stopSending')
                  : t('chat.sendMessage')
            }
            position="top"
          >
            <button
              onClick={(chatStream.isLoading || chatStream.isStreaming) ? handleStopSending : handleSendMessage}
              disabled={
                !!chatStream.toolCallInterrupt || // ✅ Disable khi có tool call interrupt
                (!(chatStream.isLoading || chatStream.isStreaming) && !message.trim() && files.length === 0)
              }
              className={`p-2 w-10 h-10 flex items-center justify-center rounded-full transition-colors ${
                chatStream.toolCallInterrupt
                  ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed bg-gray-100 dark:bg-gray-700' // ✅ Style khi có tool call interrupt
                  : (chatStream.isLoading || chatStream.isStreaming)
                    ? 'text-white bg-red-500 hover:bg-red-600'
                    : (message.trim() || files.length > 0)
                      ? 'text-white bg-primary hover:bg-primary/90'
                      : 'text-gray-400 dark:text-gray-600 cursor-not-allowed bg-gray-100 dark:bg-gray-700'
                }`}
              aria-label={
                chatStream.toolCallInterrupt
                  ? 'Đang chờ xác nhận tool call'
                  : (chatStream.isLoading || chatStream.isStreaming)
                    ? t('chat.stopSending')
                    : t('chat.sendMessage')
              }
            >
              <Icon name={(chatStream.isLoading || chatStream.isStreaming) ? "x" : "send"} size="md" />
            </button>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default ChatInputBox;
