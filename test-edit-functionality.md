# Test Edit Message Functionality

## C<PERSON>c thay đổi đã thực hiện:

### 1. S<PERSON>a việ<PERSON> g<PERSON> messageId cho user message
- **File**: `src/shared/hooks/common/useChatStream.ts`
- **Thay đổi**: <PERSON><PERSON><PERSON> nh<PERSON> `onMessageCreated` đ<PERSON> gán messageId cho cả supervisor và user role
- **Logic**: 
  - <PERSON><PERSON> messageId cho currentStreamingMessageRef nếu role là supervisor hoặc user
  - Thêm logic riêng để gán messageId cho user message cuối cùng chưa có messageId

### 2. Thêm chức năng edit message
- **Files modified**:
  - `src/shared/components/common/ChatMessage/ChatMessage.tsx`
  - `src/shared/components/layout/chat-panel/ChatInputBox.tsx`
  - `src/shared/components/layout/chat-panel/ChatInput.tsx`
  - `src/shared/components/layout/chat-panel/ChatContent.tsx`
  - `src/shared/components/layout/chat-panel/ChatPanel.tsx`

### 3. <PERSON><PERSON>ng hoạt động edit message:

1. **ChatMessage component**:
   - Thêm props `isLastUserMessage` và `onEditMessage`
   - Hiển thị edit button (icon bút chì) cho tin nhắn user cuối cùng
   - Khi click edit button, gọi `onEditMessage(content)`

2. **ChatContent component**:
   - Thêm prop `onEditMessage`
   - Logic xác định tin nhắn user cuối cùng trong `visibleItems`
   - Truyền `isLastUserMessage=true` và `onEditMessage` cho ChatMessage

3. **ChatInputBox component**:
   - Thêm props `editContent` và `onEditComplete`
   - Khi nhận `editContent`, tự động set vào textarea và focus
   - Gọi `onEditComplete` sau khi set content

4. **ChatInput component**:
   - Truyền `editContent` và `onEditComplete` xuống ChatInputBox

5. **ChatPanel component**:
   - State: `editContent`, `editingMessageId`
   - Handler `handleEditMessage`: 
     - Tìm tin nhắn user cuối cùng
     - Set editContent để truyền xuống ChatInputBox
     - TODO: Xóa assistant messages sau tin nhắn đó
   - Handler `handleEditComplete`: Clear edit state

## Cần hoàn thiện:

### 1. Logic xóa assistant messages
Hiện tại chỉ identify được messages cần xóa nhưng chưa implement việc xóa thực tế.

### 2. Test functionality
Cần test để đảm bảo:
- Edit button chỉ hiển thị cho tin nhắn user cuối cùng
- Click edit button load content vào input box
- Sau khi edit và gửi, assistant messages sau đó bị xóa

## Cách test:

1. Mở chat interface
2. Gửi một tin nhắn user
3. Đợi AI response
4. Gửi thêm tin nhắn user khác
5. Đợi AI response
6. Hover vào tin nhắn user cuối cùng → should see edit icon
7. Click edit icon → content should load into input box
8. Edit content và gửi → assistant messages sau tin nhắn đó should be removed
