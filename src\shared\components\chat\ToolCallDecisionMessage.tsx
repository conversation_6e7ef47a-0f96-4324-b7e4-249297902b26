/**
 * ToolCallDecisionMessage Component
 * Hi<PERSON>n thị kết quả quyết định tool call trong lịch sử chat
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/shared/components/common';

export interface ToolCallDecisionMessageProps {
  /**
   * Quyết định của người dùng
   */
  decision: 'yes' | 'no' | 'always';

  /**
   * Timestamp của quyết định
   */
  timestamp?: Date;

  /**
   * Custom className
   */
  className?: string;
}

/**
 * ToolCallDecisionMessage Component
 */
const ToolCallDecisionMessage: React.FC<ToolCallDecisionMessageProps> = ({
  decision,
  timestamp,
  className = ''
}) => {
  const { t } = useTranslation(['chat']);

  // Xác định icon và màu sắc dựa trên decision
  const getDecisionConfig = () => {
    switch (decision) {
      case 'yes':
        return {
          icon: 'check-circle',
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800',
          text: t('chat.toolCallApproved', 'Đã phê duyệt tool call')
        };
      case 'no':
        return {
          icon: 'x-circle',
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          text: t('chat.toolCallRejected', 'Đã từ chối tool call')
        };
      case 'always':
        return {
          icon: 'shield-check',
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          text: t('chat.toolCallAlwaysApproved', 'Đã phê duyệt tự động tool call')
        };
      default:
        return {
          icon: 'question-mark-circle',
          color: 'text-gray-600 dark:text-gray-400',
          bgColor: 'bg-gray-50 dark:bg-gray-900/20',
          borderColor: 'border-gray-200 dark:border-gray-800',
          text: t('chat.toolCallUnknownDecision', 'Quyết định không xác định')
        };
    }
  };

  const config = getDecisionConfig();

  return (
    <div className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg border ${config.bgColor} ${config.borderColor} ${className}`}>
      {/* Icon */}
      <Icon 
        name={config.icon as any} 
        size="sm" 
        className={config.color}
      />
      
      {/* Text */}
      <span className={`text-sm font-medium ${config.color}`}>
        {config.text}
      </span>
      
      {/* Timestamp (optional) */}
      {timestamp && (
        <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
          {timestamp.toLocaleTimeString()}
        </span>
      )}
    </div>
  );
};

export default ToolCallDecisionMessage;
