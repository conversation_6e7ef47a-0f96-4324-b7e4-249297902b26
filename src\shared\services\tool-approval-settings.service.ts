/**
 * ToolApprovalSettingsService
 * Service để manage tool approval settings trong localStorage
 */

export interface ToolApprovalSettings {
  /**
   * Global setting - always approve all tools
   */
  alwaysApproveAllTools: boolean;

  /**
   * Per-agent settings - always approve tools for specific agents
   */
  agentSettings: Record<string, {
    alwaysApprove: boolean;
    enabledAt: number; // timestamp
  }>;

  /**
   * Per-tool settings - always approve specific tools
   */
  toolSettings: Record<string, {
    alwaysApprove: boolean;
    enabledAt: number; // timestamp
  }>;

  /**
   * Last updated timestamp
   */
  lastUpdated: number;
}

class ToolApprovalSettingsService {
  private readonly STORAGE_KEY = 'tool_approval_settings';
  private readonly DEFAULT_SETTINGS: ToolApprovalSettings = {
    alwaysApproveAllTools: false,
    agentSettings: {},
    toolSettings: {},
    lastUpdated: Date.now()
  };

  /**
   * Get current settings từ localStorage
   */
  getSettings(): ToolApprovalSettings {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) {
        return { ...this.DEFAULT_SETTINGS };
      }

      const parsed = JSON.parse(stored) as ToolApprovalSettings;
      
      // Validate structure
      if (!parsed || typeof parsed !== 'object') {
        return { ...this.DEFAULT_SETTINGS };
      }

      // Merge với defaults để đảm bảo có đầy đủ fields
      return {
        alwaysApproveAllTools: parsed.alwaysApproveAllTools || false,
        agentSettings: parsed.agentSettings || {},
        toolSettings: parsed.toolSettings || {},
        lastUpdated: parsed.lastUpdated || Date.now()
      };
    } catch (error) {
      return { ...this.DEFAULT_SETTINGS };
    }
  }

  /**
   * Save settings vào localStorage
   */
  private saveSettings(settings: ToolApprovalSettings): void {
    try {
      settings.lastUpdated = Date.now();
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('[ToolApprovalSettings] Failed to save settings:', error);
    }
  }

  /**
   * Enable always approve for all tools globally
   */
  enableAlwaysApproveAllTools(): void {
    const settings = this.getSettings();
    settings.alwaysApproveAllTools = true;
    this.saveSettings(settings);
  }

  /**
   * Disable always approve for all tools globally
   */
  disableAlwaysApproveAllTools(): void {
    const settings = this.getSettings();
    settings.alwaysApproveAllTools = false;
    this.saveSettings(settings);
  }

  /**
   * Enable always approve for specific agent
   */
  enableAlwaysApproveForAgent(agentId: string): void {
    const settings = this.getSettings();
    settings.agentSettings[agentId] = {
      alwaysApprove: true,
      enabledAt: Date.now()
    };
    this.saveSettings(settings);
  }

  /**
   * Disable always approve for specific agent
   */
  disableAlwaysApproveForAgent(agentId: string): void {
    const settings = this.getSettings();
    if (settings.agentSettings[agentId]) {
      settings.agentSettings[agentId].alwaysApprove = false;
    }
    this.saveSettings(settings);
  }

  /**
   * Enable always approve for specific tool
   */
  enableAlwaysApproveForTool(toolName: string): void {
    const settings = this.getSettings();
    settings.toolSettings[toolName] = {
      alwaysApprove: true,
      enabledAt: Date.now()
    };
    this.saveSettings(settings);
  }

  /**
   * Disable always approve for specific tool
   */
  disableAlwaysApproveForTool(toolName: string): void {
    const settings = this.getSettings();
    if (settings.toolSettings[toolName]) {
      settings.toolSettings[toolName].alwaysApprove = false;
    }
    this.saveSettings(settings);
  }

  /**
   * Check if should always approve tools
   * Priority: Global > Agent > Tool
   */
  shouldAlwaysApprove(options?: {
    agentId?: string;
    toolName?: string;
  }): boolean {
    const settings = this.getSettings();

    // 1. Check global setting first (highest priority)
    if (settings.alwaysApproveAllTools) {
      return true;
    }

    // 2. Check agent-specific setting
    if (options?.agentId && settings.agentSettings[options.agentId]?.alwaysApprove) {
      return true;
    }

    // 3. Check tool-specific setting
    if (options?.toolName && settings.toolSettings[options.toolName]?.alwaysApprove) {
      return true;
    }

    return false;
  }

  /**
   * Clear all settings
   */
  clearAllSettings(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('[ToolApprovalSettings] Failed to clear settings:', error);
    }
  }

  /**
   * Get settings summary for debugging
   */
  getSettingsSummary(): {
    globalEnabled: boolean;
    agentCount: number;
    toolCount: number;
    lastUpdated: string;
  } {
    const settings = this.getSettings();
    return {
      globalEnabled: settings.alwaysApproveAllTools,
      agentCount: Object.keys(settings.agentSettings).length,
      toolCount: Object.keys(settings.toolSettings).length,
      lastUpdated: new Date(settings.lastUpdated).toISOString()
    };
  }
}

// Export singleton instance
export const toolApprovalSettingsService = new ToolApprovalSettingsService();
