/**
 * Utility functions để tạo MessageRequest dễ dàng hơn
 */

import {
  ChatMessageRequest,
  ChatContentBlock,
  ChatAttachmentContext
} from '@/shared/types/chat-streaming.types';

/**
 * Tạo ChatMessageRequest cho tin nhắn text đơn giản
 */
export function createTextMessage(
  content: string,
  threadId: string,
  options?: {
    alwaysApproveToolCall?: boolean;
  }
): ChatMessageRequest {
  return {
    contentBlocks: [{ type: 'text', content }],
    threadId,
    alwaysApproveToolCall: options?.alwaysApproveToolCall
  };
}

/**
 * Tạo MessageRequest cho reply message
 */
export function createReplyMessage(
  content: string,
  threadId: string,
  replyToMessageId: string,
  options?: {
    alwaysApproveToolCall?: boolean;
  }
): ChatMessageRequest {
  return {
    contentBlocks: [{ type: 'text', content }],
    threadId,
    replyToMessageId,
    alwaysApproveToolCall: options?.alwaysApproveToolCall
  };
}

/**
 * Tạo ChatMessageRequest cho modify message
 */
export function createModifyMessage(
  content: string,
  threadId: string,
  messageId: string,
  options?: {
    alwaysApproveToolCall?: boolean;
  }
): ChatMessageRequest {
  return {
    contentBlocks: [{ type: 'text', content }],
    threadId,
    messageId,
    alwaysApproveToolCall: options?.alwaysApproveToolCall
  };
}

/**
 * Tạo ChatMessageRequest với attachments
 */
export function createMessageWithAttachments(
  content: string,
  threadId: string,
  attachments: Array<{
    type: 'file' | 'image';
    id: string;
  }>,
  options?: {
    replyToMessageId?: string;
    messageId?: string;
    alwaysApproveToolCall?: boolean;
  }
): ChatMessageRequest {
  const contentBlocks: ChatContentBlock[] = [
    { type: 'text', content }
  ];

  const attachmentContext: ChatAttachmentContext[] = [];

  // Thêm attachment blocks và context
  attachments.forEach(attachment => {
    if (attachment.type === 'file') {
      contentBlocks.push({ type: 'file', fileId: attachment.id });
      attachmentContext.push({ type: 'file', fileId: attachment.id });
    } else if (attachment.type === 'image') {
      contentBlocks.push({ type: 'image', imageId: attachment.id });
      attachmentContext.push({ type: 'image', imageId: attachment.id });
    }
  });

  return {
    contentBlocks,
    threadId,
    attachmentContext,
    replyToMessageId: options?.replyToMessageId,
    messageId: options?.messageId,
    alwaysApproveToolCall: options?.alwaysApproveToolCall
  };
}

/**
 * Tạo ChatMessageRequest từ content blocks tùy chỉnh
 */
export function createCustomMessage(
  contentBlocks: ChatContentBlock[],
  threadId: string,
  options?: {
    replyToMessageId?: string;
    messageId?: string;
    alwaysApproveToolCall?: boolean;
    attachmentContext?: ChatAttachmentContext[];
  }
): ChatMessageRequest {
  return {
    contentBlocks,
    threadId,
    replyToMessageId: options?.replyToMessageId,
    messageId: options?.messageId,
    alwaysApproveToolCall: options?.alwaysApproveToolCall,
    attachmentContext: options?.attachmentContext
  };
}

/**
 * Kiểm tra xem ChatMessageRequest có phải là reply không
 */
export function isReplyMessage(request: ChatMessageRequest): boolean {
  return !!request.replyToMessageId;
}

/**
 * Kiểm tra xem ChatMessageRequest có phải là modify không
 */
export function isModifyMessage(request: ChatMessageRequest): boolean {
  return !!request.messageId;
}

/**
 * Kiểm tra xem ChatMessageRequest có attachments không
 */
export function hasAttachments(request: ChatMessageRequest): boolean {
  return !!request.attachmentContext && request.attachmentContext.length > 0;
}

/**
 * Lấy text content từ ChatMessageRequest
 */
export function getTextContent(request: ChatMessageRequest): string {
  const textBlock = request.contentBlocks.find(block => block.type === 'text');
  return textBlock?.content || '';
}

/**
 * Lấy danh sách file IDs từ ChatMessageRequest
 */
export function getFileIds(request: ChatMessageRequest): string[] {
  return request.contentBlocks
    .filter(block => block.type === 'file' && block.fileId)
    .map(block => block.fileId!);
}

/**
 * Lấy danh sách image IDs từ ChatMessageRequest
 */
export function getImageIds(request: ChatMessageRequest): string[] {
  return request.contentBlocks
    .filter(block => block.type === 'image' && block.imageId)
    .map(block => block.imageId!);
}

/**
 * Validate ChatMessageRequest
 */
export function validateMessageRequest(request: ChatMessageRequest): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!request.threadId) {
    errors.push('threadId is required');
  }

  if (!request.contentBlocks || request.contentBlocks.length === 0) {
    errors.push('contentBlocks cannot be empty');
  }

  if (request.replyToMessageId && request.messageId) {
    errors.push('Cannot have both replyToMessageId and messageId');
  }

  // Validate content blocks
  request.contentBlocks.forEach((block, index) => {
    if (!block.type) {
      errors.push(`contentBlocks[${index}]: type is required`);
    }

    if (block.type === 'text' && !block.content) {
      errors.push(`contentBlocks[${index}]: content is required for text blocks`);
    }

    if (block.type === 'file' && !block.fileId) {
      errors.push(`contentBlocks[${index}]: fileId is required for file blocks`);
    }

    if (block.type === 'image' && !block.imageId) {
      errors.push(`contentBlocks[${index}]: imageId is required for image blocks`);
    }

    if (block.type === 'tool_call_decision' && !block.decision) {
      errors.push(`contentBlocks[${index}]: decision is required for tool_call_decision blocks`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Clone ChatMessageRequest với modifications
 */
export function cloneMessageRequest(
  request: ChatMessageRequest,
  modifications?: Partial<ChatMessageRequest>
): ChatMessageRequest {
  return {
    ...request,
    contentBlocks: [...request.contentBlocks],
    attachmentContext: request.attachmentContext ? [...request.attachmentContext] : undefined,
    ...modifications
  };
}
