@import 'shared/styles/animation.css';
@import 'shared/styles/responsive.css';
@import 'shared/styles/theme.css';
@import 'shared/styles/scrollbar.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    font-family: var(--font-family);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    min-height: 100vh;
    background-color: var(--color-background);
    color: var(--color-foreground);
  }

  a {
    font-weight: var(--font-weight-medium);
    color: var(--color-primary);
    text-decoration: none;
  }

  a:hover {
    color: var(--color-primary-600);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: var(--font-weight-semibold);
  }
}

@layer components {
  .btn {
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    transition: all 0.2s;
    cursor: pointer;
  }

  .btn-primary {
    background-color: var(--color-primary);
    color: var(--color-primary-foreground);
  }

  .btn-primary:hover {
    opacity: 0.9;
  }

  .btn-secondary {
    background-color: var(--color-secondary);
    color: var(--color-secondary-foreground);
  }

  .btn-secondary:hover {
    opacity: 0.9;
  }

  .btn-outline {
    border: 1px solid var(--color-primary);
    color: var(--color-primary);
  }

  .btn-outline:hover {
    background-color: var(--color-primary);
    color: var(--color-primary-foreground);
  }

  .card {
    background-color: var(--color-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: box-shadow 0.2s;
  }

  .card:hover {
    box-shadow: var(--shadow-lg);
  }

  .input {
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-lg);
    background-color: var(--color-card-muted);
    border: 0;
    color: var(--color-foreground);
  }

  /* Border chỉ trong dark mode */
  .dark .input {
    border: 1px solid var(--color-border);
  }

  .input:focus {
    outline: none;
  }

  /* Focus border chỉ trong dark mode */
  .dark .input:focus {
    border-color: var(--color-primary);
  }

  /* Loại bỏ hiệu ứng ring màu đỏ khi focus */
  *:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Fix z-index for dropdown menus to appear above cards and other elements */
  .select-dropdown {
    z-index: 99999 !important;
    position: fixed !important;
  }

  /* Ensure form containers don't interfere with dropdowns */
  .slide-form-container {
    position: relative;
    z-index: auto;
  }

  /* Card components should not have high z-index that interferes with dropdowns */
  .card {
    position: relative;
    z-index: auto;
  }

  /* CollapsibleCard specific fixes for dropdown overflow */
  .card.overflow-visible {
    overflow: visible !important;
  }

  /* Ensure dropdown containers can overflow their parents */
  .dropdown-container {
    position: relative;
    z-index: 1;
  }

  /* Form sections should allow overflow for dropdowns */
  .form-section {
    overflow: visible;
  }

  /* Chỉ áp dụng border màu primary khi focus trong dark mode */
  .dark input:focus,
  .dark select:focus,
  .dark textarea:focus,
  .dark button:focus {
    border-color: var(--color-primary) !important;
  }

  /* Form styles */
  .form-item {
    margin-bottom: var(--spacing-4);
  }

  .form-item label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: var(--font-weight-medium);
  }

  .form-item label.required::after {
    content: '*';
    color: var(--color-error);
    margin-left: var(--spacing-1);
  }

  .form-item .error {
    color: var(--color-error);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
  }

  .form-item .help-text {
    color: var(--color-muted);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
  }

  /* Auto Approve Switch Styles */
  .auto-approve-switch {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    height: 16px !important;
    width: 40px !important;
    border-radius: 8px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    cursor: pointer !important;
    outline: none !important;
    border: none !important;
    padding: 0 !important;
    margin-bottom: -5px;
    /* Light mode colors */
    background: #e2e8f0 !important; /* slate-200 */
    border: 1px solid #cbd5e1 !important; /* slate-300 */
  }

  .auto-approve-switch:hover {
    transform: scale(1.05) !important;
    background: #cbd5e1 !important; /* slate-300 - darker on hover */
  }

  .auto-approve-switch.enabled {
    background: var(--color-primary) !important; /* Sử dụng màu đỏ theme */
    border: 1px solid var(--color-primary) !important;
    box-shadow: 0 4px 12px var(--color-primary-shadow, rgba(239, 68, 68, 0.3)) !important;
  }

  .auto-approve-switch.enabled:hover {
    background: var(--color-primary-hover, var(--color-primary)) !important;
    box-shadow: 0 6px 16px var(--color-primary-shadow, rgba(239, 68, 68, 0.4)) !important;
    transform: scale(1.05) !important;
    opacity: 0.9 !important;
  }

  /* Dark mode styles */
  .dark .auto-approve-switch {
    background: #334155 !important; /* slate-700 */
    border: 1px solid #475569 !important; /* slate-600 */
  }

  .dark .auto-approve-switch:hover {
    background: #475569 !important; /* slate-600 */
  }

  .dark .auto-approve-switch.enabled {
    background: var(--color-primary) !important; /* Sử dụng màu đỏ theme */
    border: 1px solid var(--color-primary) !important;
    box-shadow: 0 4px 12px var(--color-primary-shadow, rgba(239, 68, 68, 0.3)) !important;
  }

  .dark .auto-approve-switch.enabled:hover {
    background: var(--color-primary-hover, var(--color-primary)) !important;
    box-shadow: 0 6px 16px var(--color-primary-shadow, rgba(239, 68, 68, 0.4)) !important;
    transform: scale(1.05) !important;
    opacity: 0.9 !important;
  }

  .auto-approve-switch-circle {
    position: absolute !important;
    top: 1px !important;
    left: 1px !important;
    width: 12px !important;
    height: 12px !important;
    border-radius: 50% !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    /* Light mode circle */
    background: #ffffff !important;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.1),
      0 1px 2px rgba(0, 0, 0, 0.06) !important;
    border: 1px solid #e2e8f0 !important; /* subtle border */
  }

  .auto-approve-switch.enabled .auto-approve-switch-circle {
    left: auto !important;
    right: 1px !important;
    background: #ffffff !important;
    box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.15),
      0 2px 4px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
  }

  /* Dark mode circle */
  .dark .auto-approve-switch-circle {
    background: #f1f5f9 !important; /* slate-100 */
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.3),
      0 1px 2px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid #cbd5e1 !important; /* slate-300 */
  }

  .dark .auto-approve-switch.enabled .auto-approve-switch-circle {
    background: #ffffff !important;
    box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.4),
      0 2px 4px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
  }

  .auto-approve-switch-text {
    position: absolute !important;
    top: 0 !important;
    bottom: 0 !important;
    right: 3px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 16px !important;
    font-size: 7px !important;
    font-weight: 600 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    pointer-events: none !important;
    user-select: none !important;
    /* Light mode text */
    color: #64748b !important; /* slate-500 */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  }

  .auto-approve-switch.enabled .auto-approve-switch-text {
    right: auto !important;
    left: 3px !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  }

  /* Dark mode text */
  .dark .auto-approve-switch-text {
    color: #94a3b8 !important; /* slate-400 */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  }

  .dark .auto-approve-switch.enabled .auto-approve-switch-text {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  }

  /* Focus states for accessibility */
  .auto-approve-switch:focus-visible {
    outline: 2px solid #3b82f6 !important;
    outline-offset: 2px !important;
  }

  .dark .auto-approve-switch:focus-visible {
    outline: 2px solid #60a5fa !important;
  }

  /* Smooth theme transition */
  .auto-approve-switch,
  .auto-approve-switch-circle,
  .auto-approve-switch-text {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
}

/* Custom gradient background */
.bg-brand-gradient {
  background: linear-gradient(to right, var(--color-primary-500), var(--color-secondary-500));
}

/* Flag clip paths for UK flag */
.clip-triangle-top-left {
  clip-path: polygon(0 0, 0 100%, 100% 0);
}

.clip-triangle-top-right {
  clip-path: polygon(0 0, 100% 0, 100% 100%);
}

.clip-triangle-bottom-left {
  clip-path: polygon(0 0, 0 100%, 100% 100%);
}

.clip-triangle-bottom-right {
  clip-path: polygon(100% 0, 0 100%, 100% 100%);
}
