# Chat Interface Enhancement PRD

## Tổng quan dự án
Tinh chỉnh giao diện chat để cải thiện trải nghiệm người dùng với các tính năng mới:
1. Loading animation với logo xoay
2. <PERSON><PERSON><PERSON> thị tin nhắn AI không có khung box
3. Hỗ trợ role supervisor/worker với avatar riêng
4. <PERSON><PERSON><PERSON> năng reply tin nhắn cũ

## Mục tiêu
- C<PERSON>i thiện UX với loading animation đẹp mắt
- Phân biệt rõ ràng các role khác nhau trong chat
- Thêm tính năng tương tác reply message
- Tối ưu hiển thị tin nhắn theo role

## Yêu cầu chức năng

### 1. Loading Animation
- Hiệu ứng loading khi nhận tin nhắn từ AI
- Logo xoay chòn chòn thay vì text "Thinking..."
- V<PERSON> trí hiển thị tương tự như hình mẫu
- Animation mượt mà và thu hút

### 2. Message Display Enhancement
- Role 'assistant': <PERSON><PERSON><PERSON><PERSON> có khung box, hiển thị trực tiếp trên nền chat
- Role 'user': Vẫn giữ khung box như hiện tại
- Role 'supervisor': Avatar riêng + xuống dòng
- Role 'worker': Avatar riêng + xuống dòng
- Phân biệt rõ ràng giữa các role

### 3. Reply Message Feature
- Click reply trên tin nhắn cũ
- Ghim tin nhắn được reply ở chat input (hiển thị mờ)
- Gửi kèm nội dung tin nhắn được reply + tin nhắn hiện tại
- UI/UX trực quan và dễ sử dụng

### 4. SSE Role Handling
- Xử lý role từ SSE events: 'supervisor' | 'worker'
- Hiển thị avatar tương ứng cho từng role
- Tách riêng tin nhắn theo role bằng xuống dòng

## Yêu cầu kỹ thuật

### Frontend Components
- Cập nhật ChatMessage component
- Thêm LoadingAnimation component
- Cập nhật ChatInput với reply functionality
- Xử lý role trong SSE streaming

### State Management
- Thêm replyMessage state
- Cập nhật message display logic
- Xử lý role-based rendering

### API Integration
- Cập nhật sendMessage API để gửi kèm reply context
- Xử lý role từ SSE events
- Maintain backward compatibility

## Acceptance Criteria

### Loading Animation
- [ ] Logo xoay mượt mà khi loading
- [ ] Vị trí hiển thị chính xác
- [ ] Animation dừng khi nhận được response

### Message Display
- [ ] AI messages không có box border
- [ ] User messages vẫn có box
- [ ] Supervisor/worker có avatar riêng
- [ ] Xuống dòng giữa các role khác nhau

### Reply Feature
- [ ] Click reply hiển thị preview ở input
- [ ] Có thể cancel reply
- [ ] Gửi message kèm reply context
- [ ] UI responsive và intuitive

### SSE Integration
- [ ] Xử lý role từ SSE events
- [ ] Hiển thị đúng avatar theo role
- [ ] Streaming text theo role

## Constraints
- Không thay đổi API backend hiện tại
- Maintain performance
- Responsive design
- Accessibility compliance

## Success Metrics
- Improved user engagement
- Reduced confusion between roles
- Increased reply usage
- Better visual hierarchy
