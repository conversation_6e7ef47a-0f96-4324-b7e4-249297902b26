/**
 * useToolCallApproval Hook
 * Hook để handle tool call approval logic
 */

import { useState, useCallback } from 'react';
import { ToolCallInterruptData } from '@/shared/services/chat-sse.service';

export interface UseToolCallApprovalOptions {
  /**
   * Callback khi approve tool call
   */
  onApprove?: (decision: 'yes' | 'no', toolCallData: ToolCallInterruptData) => Promise<void>;

  /**
   * Callback khi dismiss approval form
   */
  onDismiss?: (toolCallData: ToolCallInterruptData) => void;

  /**
   * Auto dismiss sau khi approve thành công
   */
  autoDismissOnSuccess?: boolean;
}

export interface UseToolCallApprovalReturn {
  /**
   * Tool call data hiện tại cần approval
   */
  toolCallData: ToolCallInterruptData | null;

  /**
   * Loading state khi đang approve
   */
  isApproving: boolean;

  /**
   * Error state nếu approve thất bại
   */
  approvalError: string | null;

  /**
   * Show approval form cho tool call data
   */
  showApprovalForm: (toolCallData: ToolCallInterruptData) => void;

  /**
   * Hide approval form
   */
  hideApprovalForm: () => void;

  /**
   * Handle approval decision
   */
  handleApproval: (decision: 'yes' | 'no') => Promise<void>;

  /**
   * Clear approval error
   */
  clearApprovalError: () => void;
}

/**
 * useToolCallApproval Hook
 */
export function useToolCallApproval(options: UseToolCallApprovalOptions = {}): UseToolCallApprovalReturn {
  const {
    onApprove,
    onDismiss,
    autoDismissOnSuccess = true
  } = options;

  // State
  const [toolCallData, setToolCallData] = useState<ToolCallInterruptData | null>(null);
  const [isApproving, setIsApproving] = useState(false);
  const [approvalError, setApprovalError] = useState<string | null>(null);

  /**
   * Show approval form
   */
  const showApprovalForm = useCallback((data: ToolCallInterruptData) => {
    console.log('[useToolCallApproval] Showing approval form for tool:', data.toolName);
    setToolCallData(data);
    setApprovalError(null);
  }, []);

  /**
   * Hide approval form
   */
  const hideApprovalForm = useCallback(() => {
    console.log('[useToolCallApproval] Hiding approval form');
    
    if (toolCallData && onDismiss) {
      onDismiss(toolCallData);
    }
    
    setToolCallData(null);
    setApprovalError(null);
    setIsApproving(false);
  }, [toolCallData, onDismiss]);

  /**
   * Handle approval decision
   */
  const handleApproval = useCallback(async (decision: 'yes' | 'no') => {
    if (!toolCallData) {
      console.warn('[useToolCallApproval] No tool call data to approve');
      return;
    }

    if (!onApprove) {
      console.warn('[useToolCallApproval] No onApprove callback provided');
      return;
    }

    try {
      setIsApproving(true);
      setApprovalError(null);

      console.log('[useToolCallApproval] Approving tool call:', {
        decision,
        toolName: toolCallData.toolName,
        threadId: toolCallData.threadId
      });

      await onApprove(decision, toolCallData);

      console.log('[useToolCallApproval] Tool call approved successfully');

      // Auto dismiss nếu được config
      if (autoDismissOnSuccess) {
        setToolCallData(null);
      }
    } catch (error) {
      console.error('[useToolCallApproval] Failed to approve tool call:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to approve tool call';
      setApprovalError(errorMessage);
    } finally {
      setIsApproving(false);
    }
  }, [toolCallData, onApprove, autoDismissOnSuccess]);

  /**
   * Clear approval error
   */
  const clearApprovalError = useCallback(() => {
    setApprovalError(null);
  }, []);

  return {
    toolCallData,
    isApproving,
    approvalError,
    showApprovalForm,
    hideApprovalForm,
    handleApproval,
    clearApprovalError
  };
}
